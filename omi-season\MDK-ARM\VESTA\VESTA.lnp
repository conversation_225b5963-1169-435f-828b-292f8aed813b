--cpu=Cortex-M4.fp.sp
".\vesta\startup_stm32f407xx.o"
".\vesta\kalman.o"
".\vesta\main.o"
".\vesta\gpio.o"
".\vesta\freertos.o"
".\vesta\adc.o"
".\vesta\can.o"
".\vesta\crc.o"
".\vesta\dma.o"
".\vesta\i2c.o"
".\vesta\rng.o"
".\vesta\rtc.o"
".\vesta\spi.o"
".\vesta\tim.o"
".\vesta\usart.o"
".\vesta\usb_device.o"
".\vesta\usbd_conf.o"
".\vesta\usbd_desc.o"
".\vesta\usbd_cdc_if.o"
".\vesta\stm32f4xx_it.o"
".\vesta\stm32f4xx_hal_msp.o"
".\vesta\pm01_api.o"
".\vesta\system_stm32f4xx.o"
".\vesta\bsp_adc.o"
".\vesta\bsp_buzzer.o"
".\vesta\bsp_can.o"
".\vesta\bsp_crc32.o"
".\vesta\bsp_delay.o"
".\vesta\bsp_flash.o"
".\vesta\bsp_fric.o"
".\vesta\bsp_imu_pwm.o"
".\vesta\bsp_i2c.o"
".\vesta\bsp_laser.o"
".\vesta\bsp_led.o"
".\vesta\bsp_rc.o"
".\vesta\bsp_rng.o"
".\vesta\bsp_spi.o"
".\vesta\bsp_usart.o"
".\vesta\bsp_servo_pwm.o"
".\vesta\calibrate_task.o"
".\vesta\can_receive.o"
".\vesta\chassis_behaviour.o"
".\vesta\chassis_task.o"
".\vesta\detect_task.o"
".\vesta\gimbal_behaviour.o"
".\vesta\gimbal_task.o"
".\vesta\ins_task.o"
".\vesta\referee.o"
".\vesta\remote_control.o"
".\vesta\test_task.o"
".\vesta\usb_task.o"
".\vesta\referee_usart_task.o"
".\vesta\led_flow_task.o"
".\vesta\ui_task.o"
".\vesta\vision.o"
".\vesta\kalman_filter.o"
".\vesta\shoot_task.o"
".\vesta\music_task.o"
".\vesta\rm_cilent_ui.o"
".\vesta\voltage_task.o"
".\vesta\vision_task.o"
".\vesta\oled.o"
".\vesta\bmi088driver.o"
".\vesta\bmi088middleware.o"
".\vesta\ist8310driver.o"
".\vesta\ist8310driver_middleware.o"
"..\components\algorithm\AHRS.lib"
".\vesta\ahrs_middleware.o"
"..\components\algorithm\arm_cortexM4lf_math.lib"
".\vesta\user_lib.o"
".\vesta\crc8_crc16.o"
".\vesta\fifo.o"
".\vesta\mem_mang4.o"
".\vesta\filter.o"
".\vesta\mymath.o"
".\vesta\ramp.o"
".\vesta\pid.o"
".\vesta\new_pid.o"
".\vesta\stm32f4xx_hal_pcd.o"
".\vesta\stm32f4xx_hal_pcd_ex.o"
".\vesta\stm32f4xx_ll_usb.o"
".\vesta\stm32f4xx_hal_rcc.o"
".\vesta\stm32f4xx_hal_rcc_ex.o"
".\vesta\stm32f4xx_hal_flash.o"
".\vesta\stm32f4xx_hal_flash_ex.o"
".\vesta\stm32f4xx_hal_flash_ramfunc.o"
".\vesta\stm32f4xx_hal_gpio.o"
".\vesta\stm32f4xx_hal_dma_ex.o"
".\vesta\stm32f4xx_hal_dma.o"
".\vesta\stm32f4xx_hal_pwr.o"
".\vesta\stm32f4xx_hal_pwr_ex.o"
".\vesta\stm32f4xx_hal_cortex.o"
".\vesta\stm32f4xx_hal.o"
".\vesta\stm32f4xx_hal_exti.o"
".\vesta\stm32f4xx_hal_adc.o"
".\vesta\stm32f4xx_hal_adc_ex.o"
".\vesta\stm32f4xx_hal_can.o"
".\vesta\stm32f4xx_hal_crc.o"
".\vesta\stm32f4xx_hal_i2c.o"
".\vesta\stm32f4xx_hal_i2c_ex.o"
".\vesta\stm32f4xx_hal_rng.o"
".\vesta\stm32f4xx_hal_rtc.o"
".\vesta\stm32f4xx_hal_rtc_ex.o"
".\vesta\stm32f4xx_hal_spi.o"
".\vesta\stm32f4xx_hal_tim.o"
".\vesta\stm32f4xx_hal_tim_ex.o"
".\vesta\stm32f4xx_hal_uart.o"
".\vesta\croutine.o"
".\vesta\event_groups.o"
".\vesta\list.o"
".\vesta\queue.o"
".\vesta\stream_buffer.o"
".\vesta\tasks.o"
".\vesta\timers.o"
".\vesta\cmsis_os.o"
".\vesta\heap_4.o"
".\vesta\port.o"
".\vesta\usbd_core.o"
".\vesta\usbd_ctlreq.o"
".\vesta\usbd_ioreq.o"
".\vesta\usbd_cdc.o"
".\vesta\ui_interface.o"
".\vesta\ui_g_dynamic_0.o"
".\vesta\ui_g_dynamic_1.o"
".\vesta\ui_g_dynamic_2.o"
".\vesta\ui_g_dynamic_3.o"
".\vesta\ui_g_ungroup_0.o"
".\vesta\ui_g_ungroup_1.o"
".\vesta\ui_g_ungroup_2.o"
".\vesta\ui_g_ungroup_3.o"
".\vesta\ui_g_ungroup_4.o"
".\vesta\ui_g_ungroup_5.o"
--library_type=microlib --strict --scatter ".\VESTA\VESTA.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "..\..\..\Documents\VESTA.map" -o .\VESTA\VESTA.axf