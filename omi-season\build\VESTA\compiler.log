>>> cc

".\application\gimbal_task.c", line 74: Error:  #20: identifier "bool" is undefined
      bool gimbal_reset_flag;
      ^
".\application\gimbal_task.c", line 75: Error:  #20: identifier "bool" is undefined
      bool rotate_flag;
      ^
".\application\gimbal_task.c", line 76: Error:  #20: identifier "bool" is undefined
      bool auto_mode;
      ^
".\application\gimbal_task.c", line 265: Error:  #148: variable "yaw_can_set_current"  has already been initialized
  static int16_t yaw_can_set_current = 0, pitch_can_set_current = 0;
                 ^
".\application\gimbal_task.c", line 265: Error:  #148: variable "pitch_can_set_current"  has already been initialized
  static int16_t yaw_can_set_current = 0, pitch_can_set_current = 0;
                                          ^
.\application\gimbal_task.c: 0 warnings, 5 errors

>>> ld

