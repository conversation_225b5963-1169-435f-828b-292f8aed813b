/*
 * Advanced Gimbal Control System
 * Based on 25_OmniInfantry_III gimbal.cpp architecture
 * Features: Multi-loop PID, feedforward control, advanced filtering
 */
#include "gimbal_task.h"
#include "main.h"
#include "cmsis_os.h"
#include "bsp_laser.h"
#include "usb_task.h"
#include "arm_math.h"
#include "CAN_receive.h"
#include "user_lib.h"
#include "detect_task.h"
#include "remote_control.h"
#include "gimbal_behaviour.h"
#include "INS_task.h"
#include "pid.h"
#include "New_pid.h"
#include "vision.h"
#include "referee.h"

// Advanced Gimbal Control System Constants
#define GIMBAL_CONTROL_FREQ 1000.0f  // 1kHz control frequency
#define PITCH_DEPRESSION -0.4f       // Pitch down limit (rad)
#define PITCH_ELEVATION 0.3f         // Pitch up limit (rad)

// Motor encoder value format, range[0-8191]
#define ecd_format(ecd)    \
  {                        \
    if ((ecd) > ECD_RANGE) \
      (ecd) -= ECD_RANGE;  \
    else if ((ecd) < 0)    \
      (ecd) += ECD_RANGE;  \
  }

// Advanced Gimbal Control Structure
typedef struct {
    // PID Controllers - Triple Loop Control
    _New_Pid_t pitch_current_pid;    // Current loop
    _New_Pid_t pitch_speed_pid;      // Speed loop
    _New_Pid_t pitch_angle_pid;      // Angle loop
    _New_Pid_t yaw_current_pid;      // Current loop
    _New_Pid_t yaw_speed_pid;        // Speed loop
    _New_Pid_t yaw_angle_pid;        // Angle loop

    // Feedforward Control
    float pitch_feedforward;
    float yaw_feedforward;

    // Low Pass Filters
    first_order_filter_type_t pitch_angle_filter;
    first_order_filter_type_t pitch_speed_filter;
    first_order_filter_type_t yaw_angle_filter;
    first_order_filter_type_t yaw_speed_filter;

    // Control targets and feedback
    float pitch_target;
    float yaw_target;
    float pitch_current_angle;
    float yaw_current_angle;
    float pitch_angular_velocity;
    float yaw_angular_velocity;

    // Motor data
    const motor_measure_t *pitch_motor;
    const motor_measure_t *yaw_motor;

    // IMU data
    const fp32 *gimbal_imu_angle;
    const fp32 *gimbal_imu_gyro;

    // Control flags
    bool gimbal_reset_flag;
    bool rotate_flag;
    bool auto_mode;

    // Output currents
    int16_t pitch_output_current;
    int16_t yaw_output_current;

} advanced_gimbal_control_t;

// Global control instance
static advanced_gimbal_control_t gimbal_ctrl;

// PID parameter blocks for integral separation
static fp32 pitch_speed_i_block[2] = {0.2f, 1.0f};
static fp32 pitch_angle_i_block[2] = {0.05f, 0.15f};
static fp32 yaw_speed_i_block[2] = {0.1f, 1.0f};
static fp32 yaw_angle_i_block[2] = {0.1f, 1.0f};

// Legacy compatibility variables
extern RC_ctrl_t rc_ctrl;
volatile float Cloud_Angle_Measure[3][4];
volatile float vision_angle_measure_yaw;
gimbal_yaw_direction_e gimbal_yaw_direction;
volatile float yaw_angle_ref, pitch_angle_ref, yaw_angle_ref_last, pitch_angle_ref_last;
volatile float yaw_angle_buf, pitch_angle_buf;
volatile uint8_t vision_get = 0;
uint16_t yaw_set_angle;

// Legacy PID structures for compatibility
_New_Pid_t GIRT_pitch_s_pid;
_New_Pid_t GIRT_pitch_encode_pid;
_New_Pid_t GIRT_pitch_tuoluo_pid;
_New_Pid_t GIRT_yaw_s_pid;
_New_Pid_t GIRT_yaw_encode_pid;
_New_Pid_t GIRT_yaw_tuoluo_pid;
_New_Pid_t GIRT_pitch_auto_speed_pid;
_New_Pid_t GIRT_pitch_auto_pid;
_New_Pid_t GIRT_yaw_auto_pid;
_New_Pid_t GIRT_yaw_auto_speed_pid;
_New_Pid_t GIRT_buff_pitch_auto_pid;
_New_Pid_t GIRT_buff_yaw_auto_pid;
_New_Pid_t GIRT_buff_auto_pitch_speed_pid;
_New_Pid_t GIRT_buff_auto_yaw_speed_pid;
_New_Pid_t GIRT_BUFF_pitch_auto_pid;
_New_Pid_t GIRT_BUFF_yaw_auto_pid;
_New_Pid_t GIRT_BUFF_auto_pitch_speed_pid;
_New_Pid_t GIRT_BUFF_auto_yaw_speed_pid;

fp32 p_speed_i_block[2] = {0.2f, 1.0f};
fp32 p_angle_i_block[2] = {0.05f, 0.15f};
fp32 y_speed_i_block[2] = {0.1f, 1.0f};
fp32 y_angle_i_block[2] = {0.1f, 1.0f};
fp32 pitch_auto_speed_i_block[2] = {0.2f, 1.0f};
fp32 pitch_auto_i_block[2] = {0.05f, 0.15f};
fp32 yaw_auto_speed_i_block[2] = {0.1f, 1.0f};
fp32 yaw_auto_i_block[2] = {0.1f, 1.0f};

#if INCLUDE_uxTaskGetStackHighWaterMark
uint32_t gimbal_high_water;
#endif

// Legacy compatibility variables and structures
gimbal_control_t gimbal_control;
static int16_t yaw_can_set_current = 0, pitch_can_set_current = 0;

// Function declarations
static void advanced_gimbal_init(void);
static void advanced_gimbal_control_loop(void);
static void advanced_gimbal_feedback_update(void);
static void advanced_gimbal_pid_calculate(void);
static void advanced_gimbal_reset(void);
static fp32 motor_ecd_to_angle_change(uint16_t ecd, uint16_t offset_ecd);

// Legacy function declarations for compatibility
void GIMBAL_AUTO_Mode_Ctrl(gimbal_control_t *gimbal_kalman_init);
static void gimbal_init(gimbal_control_t *init);
static void gimbal_set_mode(gimbal_control_t *set_mode);
static void gimbal_feedback_update(gimbal_control_t *feedback_update);
static void gimbal_mode_change_control_transit(gimbal_control_t *mode_change);
static void gimbal_set_control(gimbal_control_t *set_control);
static void gimbal_control_loop(gimbal_control_t *control_loop);
static void gimbal_motor_absolute_angle_control(gimbal_motor_t *gimbal_motor);
static void gimbal_motor_relative_angle_control(gimbal_motor_t *gimbal_motor);
static void gimbal_motor_raw_angle_control(gimbal_motor_t *gimbal_motor);
static void gimbal_absolute_angle_limit(gimbal_motor_t *gimbal_motor, fp32 add);
static void gimbal_relative_angle_limit(gimbal_motor_t *gimbal_motor, fp32 add);
static void gimbal_PID_init(gimbal_PID_t *pid, fp32 maxout, fp32 intergral_limit, fp32 kp, fp32 ki, fp32 kd);
static void gimbal_PID_clear(gimbal_PID_t *pid_clear);
static fp32 gimbal_PID_calc(gimbal_PID_t *pid, fp32 get, fp32 set, fp32 error_delta);
static void calc_gimbal_cali(const gimbal_step_cali_t *gimbal_cali, uint16_t *yaw_offset, uint16_t *pitch_offset, fp32 *max_yaw, fp32 *min_yaw, fp32 *max_pitch, fp32 *min_pitch);

#if GIMBAL_TEST_MODE
static void J_scope_gimbal_test(void);
#endif
/**
 * @brief          ������̨�����趨ֵ������ֵ��ͨ��gimbal_behaviour_control_set�������õ�
 * @param[out]     gimbal_set_control:"gimbal_control"����ָ��.
 * @retval         none
 */
static void gimbal_set_control(gimbal_control_t *set_control);
/**
 * @brief          ����ѭ�������ݿ����趨ֵ������������ֵ�����п���
 * @param[out]     gimbal_control_loop:"gimbal_control"����ָ��.
 * @retval         none
 */
static void gimbal_control_loop(gimbal_control_t *control_loop);

/**
 * @brief          ��̨����ģʽ:GIMBAL_MOTOR_GYRO��ʹ�������Ǽ����ŷ���ǽ��п���
 * @param[out]     gimbal_motor:yaw�������pitch���
 * @retval         none
 */
static void gimbal_motor_absolute_angle_control(gimbal_motor_t *gimbal_motor);
/**
 * @brief          ��̨����ģʽ:GIMBAL_MOTOR_ENCONDE��ʹ�ñ�����Խǽ��п���
 * @param[out]     gimbal_motor:yaw�������pitch���
 * @retval         none
 */
static void gimbal_motor_relative_angle_control(gimbal_motor_t *gimbal_motor);

/**
 * @brief          ��̨����ģʽ:GIMBAL_MOTOR_RAW������ֱֵ�ӷ��͵�CAN����.
 * @param[out]     gimbal_motor:yaw�������pitch���
 * @retval         none
 */
static void gimbal_motor_raw_angle_control(gimbal_motor_t *gimbal_motor);
/**
 * @brief          ��GIMBAL_MOTOR_GYROģʽ�����ƽǶ��趨,��ֹ�������
 * @param[out]     gimbal_motor:yaw�������pitch���
 * @retval         none
 */
static void gimbal_absolute_angle_limit(gimbal_motor_t *gimbal_motor, fp32 add);

/**
 * @brief          ��GIMBAL_MOTOR_ENCONDEģʽ�����ƽǶ��趨,��ֹ�������
 * @param[out]     gimbal_motor:yaw�������pitch���
 * @retval         none
 */
static void gimbal_relative_angle_limit(gimbal_motor_t *gimbal_motor, fp32 add);

/**
 * @brief          ��̨�Ƕ�PID��ʼ��, ��Ϊ�Ƕȷ�Χ��(-pi,pi)��������PID.c��PID
 * @param[out]     pid:��̨PIDָ��
 * @param[in]      maxout: pid������
 * @param[in]      intergral_limit: pid���������
 * @param[in]      kp: pid kp
 * @param[in]      ki: pid ki
 * @param[in]      kd: pid kd
 * @retval         none
 */
static void gimbal_PID_init(gimbal_PID_t *pid, fp32 maxout, fp32 intergral_limit, fp32 kp, fp32 ki, fp32 kd);
/**
 * @brief          ��̨PID��������pid��out,iout
 * @param[out]     pid_clear:"gimbal_control"����ָ��.
 * @retval         none
 */
static void gimbal_PID_clear(gimbal_PID_t *pid_clear);
/**
 * @brief          ��̨�Ƕ�PID����, ��Ϊ�Ƕȷ�Χ��(-pi,pi)��������PID.c��PID
 * @param[out]     pid:��̨PIDָ��
 * @param[in]      get: �Ƕȷ���
 * @param[in]      set: �Ƕ��趨
 * @param[in]      error_delta: ���ٶ�
 * @retval         pid ���
 */
static fp32 gimbal_PID_calc(gimbal_PID_t *pid, fp32 get, fp32 set, fp32 error_delta);
/**
 * @brief          ��̨У׼����
 * @param[in]      gimbal_cali: У׼����
 * @param[out]     yaw_offset:yaw�����̨��ֵ
 * @param[out]     pitch_offset:pitch �����̨��ֵ
 * @param[out]     max_yaw:yaw �������е�Ƕ�
 * @param[out]     min_yaw: yaw �����С��е�Ƕ�
 * @param[out]     max_pitch: pitch �������е�Ƕ�
 * @param[out]     min_pitch: pitch �����С��е�Ƕ�
 * @retval         none
 */
static void calc_gimbal_cali(const gimbal_step_cali_t *gimbal_cali, uint16_t *yaw_offset, uint16_t *pitch_offset, fp32 *max_yaw, fp32 *min_yaw, fp32 *max_pitch, fp32 *min_pitch);

#if GIMBAL_TEST_MODE
// j-scope ����pid����
static void J_scope_gimbal_test(void);
#endif

// gimbal control data
//��̨���������������
gimbal_control_t gimbal_control;
//gimbal_control.gimbal_pitch_motor.gimbal_motor_measure.ecd//4968-5981;
// motor current
//���͵ĵ������
static int16_t yaw_can_set_current = 0, pitch_can_set_current = 0;

/**
 * @brief          gimbal task, osDelay GIMBAL_CONTROL_TIME (1ms)
 * @param[in]      pvParameters: null
 * @retval         none
 */
/**
 * @brief          ��̨���񣬼�� GIMBAL_CONTROL_TIME 1ms
 * @param[in]      pvParameters: ��
 * @retval         none
 */

void gimbal_task(void const *pvParameters)
{

  //�ȴ������������������������
  // wait a time
  vTaskDelay(GIMBAL_TASK_INIT_TIME);
  // gimbal init
  //��̨��ʼ��
  gimbal_init(&gimbal_control);
  // shoot init
  // wait for all motor online
  //�жϵ���Ƿ�����
  while (toe_is_error(YAW_GIMBAL_MOTOR_TOE) || toe_is_error(PITCH_GIMBAL_MOTOR_TOE))
  {
    vTaskDelay(GIMBAL_CONTROL_TIME);
    gimbal_feedback_update(&gimbal_control); //��̨���ݷ���
  }
	laser_on();
  while (1)
  {
    gimbal_set_mode(&gimbal_control);                    //������̨����ģʽ
    gimbal_mode_change_control_transit(&gimbal_control); //����ģʽ�л� �������ݹ���
    gimbal_feedback_update(&gimbal_control);             //��̨���ݷ���
    gimbal_set_control(&gimbal_control);                 //������̨������
    gimbal_control_loop(&gimbal_control);                //��̨����PID����
#if YAW_TURN
    yaw_can_set_current = -gimbal_control.gimbal_yaw_motor.given_current;
#else
    yaw_can_set_current = gimbal_control.gimbal_yaw_motor.given_current;
#endif

#if PITCH_TURN
    pitch_can_set_current = -gimbal_control.gimbal_pitch_motor.given_current;
#else
    pitch_can_set_current = gimbal_control.gimbal_pitch_motor.given_current;
#endif
    if (!(toe_is_error(YAW_GIMBAL_MOTOR_TOE) && toe_is_error(PITCH_GIMBAL_MOTOR_TOE)))
    {
      if (toe_is_error(DBUS_TOE))
      {
        CAN_cmd_gimbal_yaw(0);
        CAN_cmd_gimbal_pitch(0);
      }
      else
      {
        CAN_cmd_gimbal_pitch(pitch_can_set_current);
        CAN_cmd_gimbal_yaw(yaw_can_set_current);
      }
    }
#if GIMBAL_TEST_MODE
    J_scope_gimbal_test();
#endif

    osDelay(GIMBAL_CONTROL_TIME);

#if INCLUDE_uxTaskGetStackHighWaterMark
    gimbal_high_water = uxTaskGetStackHighWaterMark(NULL);
#endif
  }
}

/**
 * @brief          ��̨У׼���ã���У׼����̨��ֵ�Լ���С����е��ԽǶ�
 * @param[in]      yaw_offse:yaw ��ֵ
 * @param[in]      pitch_offset:pitch ��ֵ
 * @param[in]      max_yaw:max_yaw:yaw �����ԽǶ�
 * @param[in]      min_yaw:yaw ��С��ԽǶ�
 * @param[in]      max_yaw:pitch �����ԽǶ�
 * @param[in]      min_yaw:pitch ��С��ԽǶ�
 * @retval         ���ؿ�
 * @waring         �������ʹ�õ�gimbal_control ��̬�������º�������������ͨ��ָ�븴��
 */
void set_cali_gimbal_hook(const uint16_t yaw_offset, const uint16_t pitch_offset, const fp32 max_yaw, const fp32 min_yaw, const fp32 max_pitch, const fp32 min_pitch)
{
  yaw_set_angle = yaw_offset ;
  gimbal_control.gimbal_yaw_motor.max_relative_angle = max_yaw;
  gimbal_control.gimbal_yaw_motor.min_relative_angle = min_yaw;

  gimbal_control.gimbal_pitch_motor.offset_ecd = pitch_offset;
  gimbal_control.gimbal_pitch_motor.max_relative_angle = max_pitch + 0.05;
  gimbal_control.gimbal_pitch_motor.min_relative_angle = min_pitch - 0.05;
}

/**
 * @brief          ��̨У׼���㣬��У׼��¼����ֵ,��� ��Сֵ����
 * @param[out]     yaw ��ֵ ָ��
 * @param[out]     pitch ��ֵ ָ��
 * @param[out]     yaw �����ԽǶ� ָ��
 * @param[out]     yaw ��С��ԽǶ� ָ��
 * @param[out]     pitch �����ԽǶ� ָ��
 * @param[out]     pitch ��С��ԽǶ� ָ��
 * @retval         ����1 �����ɹ�У׼��ϣ� ����0 ����δУ׼��
 * @waring         �������ʹ�õ�gimbal_control ��̬�������º�������������ͨ��ָ�븴��
 */
bool_t cmd_cali_gimbal_hook(uint16_t *yaw_offset, uint16_t *pitch_offset, fp32 *max_yaw, fp32 *min_yaw, fp32 *max_pitch, fp32 *min_pitch)
{
  if (gimbal_control.gimbal_cali.step == 0)
  {
    gimbal_control.gimbal_cali.step = GIMBAL_CALI_START_STEP;
    //�������ʱ������ݣ���Ϊ��ʼ���ݣ����ж������Сֵ
    gimbal_control.gimbal_cali.max_pitch = gimbal_control.gimbal_pitch_motor.absolute_angle;
    gimbal_control.gimbal_cali.max_pitch_ecd = gimbal_control.gimbal_pitch_motor.gimbal_motor_measure->ecd;
    gimbal_control.gimbal_cali.max_yaw = gimbal_control.gimbal_yaw_motor.absolute_angle;
    gimbal_control.gimbal_cali.max_yaw_ecd = gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd;
    gimbal_control.gimbal_cali.min_pitch = gimbal_control.gimbal_pitch_motor.absolute_angle;
    gimbal_control.gimbal_cali.min_pitch_ecd = gimbal_control.gimbal_pitch_motor.gimbal_motor_measure->ecd;
    gimbal_control.gimbal_cali.min_yaw = gimbal_control.gimbal_yaw_motor.absolute_angle;
    gimbal_control.gimbal_cali.min_yaw_ecd = gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd;
    return 0;
  }
  else if (gimbal_control.gimbal_cali.step == GIMBAL_CALI_END_STEP)
  {
    calc_gimbal_cali(&gimbal_control.gimbal_cali, yaw_offset, pitch_offset, max_yaw, min_yaw, max_pitch, min_pitch);
    (*max_yaw) -= GIMBAL_CALI_REDUNDANT_ANGLE;
    (*min_yaw) += GIMBAL_CALI_REDUNDANT_ANGLE;
    (*max_pitch) -= GIMBAL_CALI_REDUNDANT_ANGLE;
    (*min_pitch) += GIMBAL_CALI_REDUNDANT_ANGLE;
    gimbal_control.gimbal_yaw_motor.offset_ecd = *yaw_offset;
    gimbal_control.gimbal_yaw_motor.max_relative_angle = *max_yaw;
    gimbal_control.gimbal_yaw_motor.min_relative_angle = *min_yaw;
    gimbal_control.gimbal_pitch_motor.offset_ecd = *pitch_offset;
    gimbal_control.gimbal_pitch_motor.max_relative_angle = *max_pitch;
    gimbal_control.gimbal_pitch_motor.min_relative_angle = *min_pitch;
    gimbal_control.gimbal_cali.step = 0;
    return 1;
  }
  else
  {
    return 0;
  }
}
/**
 * @brief          ��̨У׼���㣬��У׼��¼����ֵ,��� ��Сֵ
 * @param[out]     yaw ��ֵ ָ��
 * @param[out]     pitch ��ֵ ָ��
 * @param[out]     yaw �����ԽǶ� ָ��
 * @param[out]     yaw ��С��ԽǶ� ָ��
 * @param[out]     pitch �����ԽǶ� ָ��
 * @param[out]     pitch ��С��ԽǶ� ָ��
 * @retval         none
 */
static void calc_gimbal_cali(const gimbal_step_cali_t *gimbal_cali, uint16_t *yaw_offset, uint16_t *pitch_offset, fp32 *max_yaw, fp32 *min_yaw, fp32 *max_pitch, fp32 *min_pitch)
{
  if (gimbal_cali == NULL || yaw_offset == NULL || pitch_offset == NULL || max_yaw == NULL || min_yaw == NULL || max_pitch == NULL || min_pitch == NULL)
  {
    return;
  }

  int16_t temp_max_ecd = 0, temp_min_ecd = 0, temp_ecd = 0;

#if YAW_TURN
  temp_ecd = gimbal_cali->min_yaw_ecd - gimbal_cali->max_yaw_ecd;

  if (temp_ecd < 0)
  {
    temp_ecd += ecd_range;
  }
  temp_ecd = gimbal_cali->max_yaw_ecd + (temp_ecd / 2);

  ecd_format(temp_ecd);
  *yaw_offset = temp_ecd;
  *max_yaw = -motor_ecd_to_angle_change(gimbal_cali->max_yaw_ecd, *yaw_offset);
  *min_yaw = -motor_ecd_to_angle_change(gimbal_cali->min_yaw_ecd, *yaw_offset);

#else

  temp_ecd = gimbal_cali->max_yaw_ecd - gimbal_cali->min_yaw_ecd;

  if (temp_ecd < 0)
  {
    temp_ecd += ECD_RANGE;
  }
  temp_ecd = gimbal_cali->max_yaw_ecd - (temp_ecd / 2);

  ecd_format(temp_ecd);
  *yaw_offset = temp_ecd;
  *max_yaw = motor_ecd_to_angle_change(gimbal_cali->max_yaw_ecd, *yaw_offset);
  *min_yaw = motor_ecd_to_angle_change(gimbal_cali->min_yaw_ecd, *yaw_offset);

#endif

#if PITCH_TURN

  temp_ecd = (int16_t)(gimbal_cali->max_pitch / MOTOR_ECD_TO_RAD);
  temp_max_ecd = gimbal_cali->max_pitch_ecd + temp_ecd;
  temp_ecd = (int16_t)(gimbal_cali->min_pitch / MOTOR_ECD_TO_RAD);
  temp_min_ecd = gimbal_cali->min_pitch_ecd + temp_ecd;

  ecd_format(temp_max_ecd);
  ecd_format(temp_min_ecd);

  temp_ecd = temp_max_ecd - temp_min_ecd;

  if (temp_ecd > HALF_ECD_RANGE)
  {
    temp_ecd -= ECD_RANGE;
  }
  else if (temp_ecd < -HALF_ECD_RANGE)
  {
    temp_ecd += ECD_RANGE;
  }

  if (temp_max_ecd > temp_min_ecd)
  {
    temp_min_ecd += ECD_RANGE;
  }

  temp_ecd = temp_max_ecd - temp_ecd / 2;

  ecd_format(temp_ecd);

  *pitch_offset = temp_ecd;

  *max_pitch = -motor_ecd_to_angle_change(gimbal_cali->max_pitch_ecd, *pitch_offset);
  *min_pitch = -motor_ecd_to_angle_change(gimbal_cali->min_pitch_ecd, *pitch_offset);

#else
  temp_ecd = (int16_t)(gimbal_cali->max_pitch / MOTOR_ECD_TO_RAD);
  temp_max_ecd = gimbal_cali->max_pitch_ecd - temp_ecd;
  temp_ecd = (int16_t)(gimbal_cali->min_pitch / MOTOR_ECD_TO_RAD);
  temp_min_ecd = gimbal_cali->min_pitch_ecd - temp_ecd;

  ecd_format(temp_max_ecd);
  ecd_format(temp_min_ecd);

  temp_ecd = temp_max_ecd - temp_min_ecd;

  if (temp_ecd > HALF_ECD_RANGE)
  {
    temp_ecd -= ECD_RANGE;
  }
  else if (temp_ecd < -HALF_ECD_RANGE)
  {
    temp_ecd += ECD_RANGE;
  }

  temp_ecd = temp_max_ecd - temp_ecd / 2;

  ecd_format(temp_ecd);

  *pitch_offset = temp_ecd;

  *max_pitch = motor_ecd_to_angle_change(gimbal_cali->max_pitch_ecd, *pitch_offset);
  *min_pitch = motor_ecd_to_angle_change(gimbal_cali->min_pitch_ecd, *pitch_offset);
#endif
}
/**
 * @brief          ����pitch �������ָ��
 * @param[in]      none
 * @retval         pitch���ָ��
 */
int get_pitch_motor_etc(void)
{
  return gimbal_control.gimbal_pitch_motor.gimbal_motor_measure->ecd;
}

/**
 * @brief          ����yaw �������ָ��
 * @param[in]      none
 * @retval         yaw���ָ��
 */
uint16_t  get_yaw_motor_etc(void)
{
  return gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd;
}

/**
 * @brief          ����yaw �������ָ��
 * @param[in]      none
 * @retval         yaw���ָ��
 */
const gimbal_motor_t *get_yaw_motor_point(void)
{
  return &gimbal_control.gimbal_yaw_motor;
}

/**
 * @brief          return pitch motor data point
 * @param[in]      none
 * @retval         pitch motor data point
 */
/**
 * @brief          ����pitch �������ָ��
 * @param[in]      none
 * @retval         pitch
 */
const gimbal_motor_t *get_pitch_motor_point(void)
{
  return &gimbal_control.gimbal_pitch_motor;
}

//�ϵ�б�±���
float Slope_Begin_Pitch = 0.0020; //���ϵ�ʱ�ƶ�����
float Slope_Begin_Yaw = 0.0020;
/**
 * @brief          ��ʼ��"gimbal_control"����������pid��ʼ���� ң����ָ���ʼ������̨���ָ���ʼ���������ǽǶ�ָ���ʼ��
 * @param[out]     init:"gimbal_control"����ָ��.
 * @retval         none
 */
static void gimbal_init(gimbal_control_t *init)
{

  static const fp32 Pitch_speed_pid[3] = {PITCH_SPEED_PID_KP, PITCH_SPEED_PID_KI, PITCH_SPEED_PID_KD};
  static const fp32 Yaw_speed_pid[3] = {YAW_SPEED_PID_KP, YAW_SPEED_PID_KI, YAW_SPEED_PID_KD};
  //�������ָ���ȡ
  init->gimbal_yaw_motor.gimbal_motor_measure = get_yaw_gimbal_motor_measure_point();
  init->gimbal_pitch_motor.gimbal_motor_measure = get_pitch_gimbal_motor_measure_point();
  //����������ָ���ȡ
  init->gimbal_INT_angle_point = get_INS_angle_point();
  init->gimbal_INT_gyro_point = get_gyro_data_point();
  //ң��������ָ���ȡ
  init->gimbal_rc_ctrl = get_remote_control_point();
  //��ʼ�����ģʽ
  init->gimbal_yaw_motor.gimbal_motor_mode = init->gimbal_yaw_motor.last_gimbal_motor_mode = GIMBAL_MOTOR_RAW;
  init->gimbal_pitch_motor.gimbal_motor_mode = init->gimbal_pitch_motor.last_gimbal_motor_mode = GIMBAL_MOTOR_RAW;
  //��ʼ��yaw���pid
  // gimbal_PID_init(&init->gimbal_yaw_motor.gimbal_motor_absolute_angle_pid, YAW_GYRO_ABSOLUTE_PID_MAX_OUT, YAW_GYRO_ABSOLUTE_PID_MAX_IOUT, YAW_GYRO_ABSOLUTE_PID_KP, YAW_GYRO_ABSOLUTE_PID_KI, YAW_GYRO_ABSOLUTE_PID_KD);
  // gimbal_PID_init(&init->gimbal_yaw_motor.gimbal_motor_relative_angle_pid, YAW_ENCODE_RELATIVE_PID_MAX_OUT, YAW_ENCODE_RELATIVE_PID_MAX_IOUT, YAW_ENCODE_RELATIVE_PID_KP, YAW_ENCODE_RELATIVE_PID_KI, YAW_ENCODE_RELATIVE_PID_KD);
  // PID_init(&init->gimbal_yaw_motor.gimbal_motor_gyro_pid, PID_POSITION, Yaw_speed_pid, YAW_SPEED_PID_MAX_OUT, YAW_SPEED_PID_MAX_IOUT);
  // //��ʼ��pitch���pid
  // gimbal_PID_init(&init->gimbal_pitch_motor.gimbal_motor_absolute_angle_pid, PITCH_GYRO_ABSOLUTE_PID_MAX_OUT, PITCH_GYRO_ABSOLUTE_PID_MAX_IOUT, PITCH_GYRO_ABSOLUTE_PID_KP, PITCH_GYRO_ABSOLUTE_PID_KI, PITCH_GYRO_ABSOLUTE_PID_KD);
  // gimbal_PID_init(&init->gimbal_pitch_motor.gimbal_motor_relative_angle_pid, PITCH_ENCODE_RELATIVE_PID_MAX_OUT, PITCH_ENCODE_RELATIVE_PID_MAX_IOUT, PITCH_ENCODE_RELATIVE_PID_KP, PITCH_ENCODE_RELATIVE_PID_KI, PITCH_ENCODE_RELATIVE_PID_KD);
  // PID_init(&init->gimbal_pitch_motor.gimbal_motor_gyro_pid, PID_POSITION, Pitch_speed_pid, PITCH_SPEED_PID_MAX_OUT, PITCH_SPEED_PID_MAX_IOUT);
  //����Ϊ�µ�PID
  //��ʼ��yaw���pid                                                                     ��ʼ��yaw���pid                                            ��ʼ��yaw���pid                             ��ʼ��yaw���pid
  New_PID_init(&GIRT_yaw_s_pid, YAW_SPEED_PID_KP, YAW_SPEED_PID_KI, YAW_SPEED_PID_KD, YAW_SPEED_PID_MAX_OUT, YAW_SPEED_PID_MAX_IOUT, y_speed_i_block, 0.0f, 0.0f, 0.0f, 0.0f);
  New_PID_init(&GIRT_yaw_encode_pid, YAW_ENCODE_RELATIVE_PID_KP, YAW_ENCODE_RELATIVE_PID_KI, YAW_ENCODE_RELATIVE_PID_KD, YAW_ENCODE_RELATIVE_PID_MAX_OUT, YAW_ENCODE_RELATIVE_PID_MAX_IOUT, y_angle_i_block, 0.0f, 0.0f, 0.0f, 0.0f);
  New_PID_init(&GIRT_yaw_tuoluo_pid, YAW_GYRO_ABSOLUTE_PID_KP, YAW_GYRO_ABSOLUTE_PID_KI, YAW_GYRO_ABSOLUTE_PID_KD, YAW_GYRO_ABSOLUTE_PID_MAX_OUT, YAW_GYRO_ABSOLUTE_PID_MAX_IOUT, y_angle_i_block, 0.0f, 0.0f, 0.0f, 0.0f);
//	Class_PID_Init(&GIRT_yaw_tuoluo_pid, YAW_GYRO_ABSOLUTE_PID_KP, YAW_GYRO_ABSOLUTE_PID_KI, YAW_GYRO_ABSOLUTE_PID_KD,YAW_GYRO_SPEED_PID_KF, YAW_GYRO_ABSOLUTE_PID_MAX_IOUT,  YAW_GYRO_ABSOLUTE_PID_MAX_OUT, 0.1, 0, 2500, 2500, 2500,1);
  //��ʼ��pitch���pid                                                          ��ʼ��pitch���pid                                               ��ʼ��pitch���pid                                             ��ʼ��pitch���pid
  New_PID_init(&GIRT_pitch_s_pid, PITCH_SPEED_PID_KP, PITCH_SPEED_PID_KI, PITCH_SPEED_PID_KD, PITCH_SPEED_PID_MAX_OUT, PITCH_SPEED_PID_MAX_IOUT, p_speed_i_block, 0.0f, 0.0f, 0.0f, 0.0f);
  New_PID_init(&GIRT_pitch_encode_pid, PITCH_ENCODE_RELATIVE_PID_KP, PITCH_ENCODE_RELATIVE_PID_KI, PITCH_ENCODE_RELATIVE_PID_KD, PITCH_ENCODE_RELATIVE_PID_MAX_OUT, PITCH_ENCODE_RELATIVE_PID_MAX_IOUT, p_angle_i_block, 0.0f, 0.0f, 0.0f, 0.0f);
  New_PID_init(&GIRT_pitch_tuoluo_pid, PITCH_GYRO_ABSOLUTE_PID_KP, PITCH_GYRO_ABSOLUTE_PID_KI, PITCH_GYRO_ABSOLUTE_PID_KD, PITCH_GYRO_ABSOLUTE_PID_MAX_OUT, PITCH_GYRO_ABSOLUTE_PID_MAX_IOUT, p_angle_i_block, 0.0f, 0.0f, 0.0f, 0.0f);
  
//	//�пƴ�PID
//	  //��ʼ��yaw���pid                                                                     ��ʼ��yaw���pid                                            ��ʼ��yaw���pid                             ��ʼ��yaw���pid
//  Class_PID_Init(&GIRT_yaw_s_pid, YAW_SPEED_PID_KP, YAW_SPEED_PID_KI, YAW_SPEED_PID_KD,YAW_SPEED_PID_KF, YAW_SPEED_PID_MAX_IOUT, YAW_SPEED_PID_MAX_OUT, 0.1, 0, 2500, 2500, 2500,1);
//  Class_PID_Init(&GIRT_yaw_encode_pid, YAW_ENCODE_RELATIVE_PID_KP, YAW_ENCODE_RELATIVE_PID_KI, YAW_ENCODE_RELATIVE_PID_KD,YAW_ENCODE_SPEED_PID_KF, YAW_ENCODE_RELATIVE_PID_MAX_IOUT, YAW_ENCODE_RELATIVE_PID_MAX_OUT, 0.1, 0, 2500, 2500, 2500,1);
//  Class_PID_Init(&GIRT_yaw_tuoluo_pid, YAW_GYRO_ABSOLUTE_PID_KP, YAW_GYRO_ABSOLUTE_PID_KI, YAW_GYRO_ABSOLUTE_PID_KD,YAW_GYRO_SPEED_PID_KF, YAW_GYRO_ABSOLUTE_PID_MAX_IOUT, YAW_GYRO_ABSOLUTE_PID_MAX_OUT, 0.1, 0, 2500, 2500, 2500,1);

//  //��ʼ��pitch���pid                                                          ��ʼ��pitch���pid                                               ��ʼ��pitch���pid                                             ��ʼ��pitch���pid
//  Class_PID_Init(&GIRT_pitch_s_pid, PITCH_SPEED_PID_KP, PITCH_SPEED_PID_KI, PITCH_SPEED_PID_KD,PITCH_SPEED_PID_KF,PITCH_SPEED_PID_MAX_IOUT, PITCH_SPEED_PID_MAX_OUT, 0.1,0,2500,2500, 2500, 1);
//  Class_PID_Init(&GIRT_pitch_encode_pid, PITCH_ENCODE_RELATIVE_PID_KP, PITCH_ENCODE_RELATIVE_PID_KI, PITCH_ENCODE_RELATIVE_PID_KD,PITCH_ENCODE_SPEED_PID_KF, PITCH_ENCODE_RELATIVE_PID_MAX_IOUT, PITCH_ENCODE_RELATIVE_PID_MAX_OUT, 0.1, 0, 2500, 2500, 2500,1);
//  Class_PID_Init(&GIRT_pitch_tuoluo_pid, PITCH_GYRO_ABSOLUTE_PID_KP, PITCH_GYRO_ABSOLUTE_PID_KI, PITCH_GYRO_ABSOLUTE_PID_KD,PITCH_GYRO_ABSOLUTE_PID_KF, PITCH_GYRO_ABSOLUTE_PID_MAX_IOUT, PITCH_GYRO_ABSOLUTE_PID_MAX_OUT, 0.1, 0, 2500, 2500, 2500,1);

	//��ʼ������ģʽPID
//  /*�ǶȻ�*/
  New_PID_init(&GIRT_pitch_auto_pid, PITCH_AUTO_PID_KP, PITCH_AUTO_PID_KI, PITCH_AUTO_PID_KD, PITCH_AUTO_PID_MAX_OUT, PITCH_AUTO_PID_MAX_IOUT, pitch_auto_i_block, 0.0f, 0.0f, 0.0f, 0.0f);
  New_PID_init(&GIRT_yaw_auto_pid, YAW_AUTO_PID_KP, YAW_AUTO_PID_KI, YAW_AUTO_PID_KD, YAW_AUTO_PID_MAX_OUT, YAW_AUTO_PID_MAX_IOUT, yaw_auto_i_block, 0.0f, 0.0f, 0.0f, 0.0f);
  /*�ٶȻ�*/
  New_PID_init(&GIRT_pitch_auto_speed_pid, PITCH_AUTO_SPEED_PID_KP, PITCH_AUTO_SPEED_PID_KI, PITCH_AUTO_SPEED_PID_KD, PITCH_AUTO_SPEED_PID_MAX_OUT, PITCH_AUTO_SPEED_PID_MAX_IOUT, pitch_auto_speed_i_block, 0.0f, 0.0f, 0.0f, 0.0f);
  New_PID_init(&GIRT_yaw_auto_speed_pid, YAW_AUTO_SPEED_PID_KP, YAW_AUTO_SPEED_PID_KI, YAW_AUTO_SPEED_PID_KD, YAW_AUTO_SPEED_PID_MAX_OUT, YAW_AUTO_SPEED_PID_MAX_IOUT, yaw_auto_speed_i_block, 0.0f, 0.0f, 0.0f, 0.0f);

	//�пƴ�PID
  /*�ǶȻ�*/
//  Class_PID_Init(&GIRT_pitch_auto_pid, PITCH_AUTO_PID_KP, PITCH_AUTO_PID_KI, PITCH_AUTO_PID_KD,PITCH_AUTO_PID_KF, PITCH_AUTO_PID_MAX_IOUT,  PITCH_AUTO_PID_MAX_OUT, 0.1, 0, 2500, 2500, 2500,1);
//  Class_PID_Init(&GIRT_yaw_auto_pid, YAW_AUTO_PID_KP, YAW_AUTO_PID_KI, YAW_AUTO_PID_KD,YAW_GYRO_SPEED_PID_KF, YAW_AUTO_PID_MAX_IOUT,  YAW_AUTO_PID_MAX_OUT, 0.1, 0, 2500, 2500, 2500,1);
//  /*�ٶȻ�*/
//  Class_PID_Init(&GIRT_pitch_auto_speed_pid, PITCH_AUTO_SPEED_PID_KP, PITCH_AUTO_SPEED_PID_KI, PITCH_AUTO_SPEED_PID_KD,PITCH_SPEED_PID_KF, PITCH_AUTO_SPEED_PID_MAX_IOUT, PITCH_AUTO_SPEED_PID_MAX_OUT, 0.1, 0, 2500, 2500, 2500,1);
//  Class_PID_Init(&GIRT_yaw_auto_speed_pid, YAW_AUTO_SPEED_PID_KP, YAW_AUTO_SPEED_PID_KI, YAW_AUTO_SPEED_PID_KD, YAW_SPEED_PID_KF,YAW_AUTO_SPEED_PID_MAX_IOUT,YAW_AUTO_SPEED_PID_MAX_OUT,0.1, 0, 2500, 2500, 2500,1);
	
	/**************���pid****************/
	  /*�ǶȻ�*/
  New_PID_init(&GIRT_buff_pitch_auto_pid, PITCH_buff_PID_KP, PITCH_buff_PID_KI, PITCH_buff_PID_KD, PITCH_buff_PID_MAX_OUT, PITCH_buff_PID_MAX_IOUT, p_angle_i_block, 0.0f, 0.0f, 0.0f, 0.0f);
  New_PID_init(&GIRT_buff_yaw_auto_pid, YAW_buff_PID_KP, YAW_buff_PID_KI, YAW_buff_PID_KD, YAW_buff_PID_MAX_OUT, YAW_buff_PID_MAX_IOUT, y_angle_i_block, 0.0f, 0.0f, 0.0f, 0.0f);
  /*�ٶȻ�*/
  New_PID_init(&GIRT_buff_auto_pitch_speed_pid, PITCH_buff_SPEED_PID_KP, PITCH_buff_SPEED_PID_KI, PITCH_buff_SPEED_PID_KD, PITCH_buff_SPEED_PID_MAX_OUT, PITCH_buff_SPEED_PID_MAX_IOUT, p_speed_i_block, 0.0f, 0.0f, 0.0f, 0.0f);
  New_PID_init(&GIRT_buff_auto_yaw_speed_pid, YAW_buff_SPEED_PID_KP, YAW_buff_SPEED_PID_KI, YAW_buff_SPEED_PID_KD, YAW_buff_SPEED_PID_MAX_OUT, YAW_buff_SPEED_PID_MAX_IOUT, y_speed_i_block, 0.0f, 0.0f, 0.0f, 0.0f);
	
	//���Ƕȴ��pid
	  /*�ǶȻ�*/
  New_PID_init(&GIRT_BUFF_pitch_auto_pid, PITCH_BUFF_PID_KP, PITCH_BUFF_PID_KI, PITCH_BUFF_PID_KD, PITCH_BUFF_PID_MAX_OUT, PITCH_BUFF_PID_MAX_IOUT, p_angle_i_block, 0.0f, 0.0f, 0.0f, 0.0f);
  New_PID_init(&GIRT_BUFF_yaw_auto_pid, YAW_BUFF_PID_KP, YAW_BUFF_PID_KI, YAW_BUFF_PID_KD, YAW_BUFF_PID_MAX_OUT, YAW_BUFF_PID_MAX_IOUT, y_angle_i_block, 0.0f, 0.0f, 0.0f, 0.0f);
  /*�ٶȻ�*/
  New_PID_init(&GIRT_BUFF_auto_pitch_speed_pid, PITCH_BUFF_SPEED_PID_KP, PITCH_BUFF_SPEED_PID_KI, PITCH_BUFF_SPEED_PID_KD, PITCH_BUFF_SPEED_PID_MAX_OUT, PITCH_BUFF_SPEED_PID_MAX_IOUT, p_speed_i_block, 0.0f, 0.0f, 0.0f, 0.0f);
  New_PID_init(&GIRT_BUFF_auto_yaw_speed_pid, YAW_BUFF_SPEED_PID_KP, YAW_BUFF_SPEED_PID_KI, YAW_BUFF_SPEED_PID_KD, YAW_BUFF_SPEED_PID_MAX_OUT, YAW_BUFF_SPEED_PID_MAX_IOUT, y_speed_i_block, 0.0f, 0.0f, 0.0f, 0.0f);
	
  // New_PID_init(&GIRT_yaw_buff_pid, YAW_AUTO_BUFF_PID_KP, YAW_AUTO_BUFF_PID_KI, YAW_AUTO_BUFF_PID_KD, YAW_AUTO_BUFF_PID_MAX_OUT, YAW_AUTO_BUFF_PID_MAX_IOUT, y_angle_i_block, 0.0f, 0.0f, 0.0f, 0.0f);
  ////////////////////////////////////////////////////////////
  //�������PID
  // gimbal_total_pid_clear(init);
  init->auto_mode = FALSE;
  //  init->gimbal_pitch_motor.offset_ecd = PITCH_MID;

  gimbal_feedback_update(init);

  init->gimbal_yaw_motor.absolute_angle_set = init->gimbal_yaw_motor.absolute_angle;
  init->gimbal_yaw_motor.relative_angle_set = init->gimbal_yaw_motor.relative_angle;
  init->gimbal_yaw_motor.motor_gyro_set = init->gimbal_yaw_motor.motor_gyro;

  init->gimbal_pitch_motor.absolute_angle_set = init->gimbal_pitch_motor.absolute_angle;
  init->gimbal_pitch_motor.relative_angle_set = init->gimbal_pitch_motor.relative_angle;
  init->gimbal_pitch_motor.motor_gyro_set = init->gimbal_pitch_motor.motor_gyro;

	init->gimbal_pitch_motor.max_relative_angle = 0.37f;
  //ƽ��������̨�ƶ����м�,��ֹ���ϵ��˦
  //	init->gimbal_yaw_motor.absolute_angle_set = RAMP_float(PITCH_MID, init->gimbal_yaw_motor.absolute_angle_set, Slope_Begin_Pitch);
  //	init->gimbal_pitch_motor.relative_angle_set = RAMP_float(YAW_MID, init->gimbal_pitch_motor.relative_angle_set, Slope_Begin_Yaw);
}

/**
 * @brief          ������̨����ģʽ����Ҫ��'gimbal_behaviour_mode_set'�����иı�
 * @param[out]     gimbal_set_mode:"gimbal_control"����ָ��.
 * @retval         none
 */
static void gimbal_set_mode(gimbal_control_t *set_mode)
{
  if (set_mode == NULL)
  {
    return;
  }
  gimbal_behaviour_mode_set(set_mode);
}
//���������̨��ֵ�ĽǶ�
static fp32 motor_ecd_to_angle_change1(uint16_t ecd)
{
  int32_t relative_ecd = ecd - 4396;
  if (relative_ecd > Half_ecd_range)
  {
    relative_ecd -= ecd_range;
  }
  else if (relative_ecd < -Half_ecd_range)
  {
    relative_ecd += ecd_range;
  }

  return relative_ecd * MOTOR_ECD_TO_RAD;
}
/**
 * @brief          ���̲������ݸ��£���������ٶȣ�ŷ���Ƕȣ��������ٶ�
 * @param[out]     gimbal_feedback_update:"gimbal_control"����ָ��.
 * @retval         none
 * @note           ��Ƕ�ΪA������ΪB����
����               �Ƕ�ת���ȣ� B = A /180 * pi;
����               ����ת�Ƕȣ� A = B /pi * 180.
����               ����pi��Բ���ʡ�
 */

static void gimbal_feedback_update(gimbal_control_t *feedback_update)
{
  if (feedback_update == NULL)
  {
    return;
  }
  //��̨���ݸ���
  feedback_update->gimbal_pitch_motor.absolute_angle = *(feedback_update->gimbal_INT_angle_point + INS_PITCH_ADDRESS_OFFSET);

  Cloud_Angle_Measure[PITCH][MECH] = feedback_update->gimbal_pitch_motor.gimbal_motor_measure->ecd;
  // (motor_ecd_to_angle_change(feedback_update->gimbal_pitch_motor.gimbal_motor_measure->ecd,
  //                                                               feedback_update->gimbal_pitch_motor.offset_ecd));
  vision_angle_measure_yaw = (*(feedback_update->gimbal_INT_angle_point + INS_YAW_ADDRESS_OFFSET) / 3.14 * 180);

  Cloud_Angle_Measure[YAW][MECH] = motor_ecd_to_angle_change(feedback_update->gimbal_yaw_motor.gimbal_motor_measure->ecd,
                                                             feedback_update->gimbal_yaw_motor.offset_ecd);

  Cloud_Angle_Measure[YAW][CHANGE] = motor_ecd_to_angle_change(feedback_update->gimbal_yaw_motor.gimbal_motor_measure->ecd,
                                                               feedback_update->gimbal_yaw_motor.offset_ecd);

  Cloud_Angle_Measure[PITCH][GYRO] = (*(feedback_update->gimbal_INT_angle_point + INS_PITCH_ADDRESS_OFFSET) / 3.14 * 180); //*(feedback_update->gimbal_INT_gyro_point + INS_GYRO_Y_ADDRESS_OFFSET) / 3.14 * 180;

  // Cloud_Angle_Measure[YAW][GYRO] =       ((arm_cos_f32(feedback_update->gimbal_pitch_motor.relative_angle)
  //                                       * (*(feedback_update->gimbal_INT_gyro_point + INS_GYRO_Z_ADDRESS_OFFSET))
  //                                       - arm_sin_f32(feedback_update->gimbal_pitch_motor.relative_angle)
  //                                       * (*(feedback_update->gimbal_INT_gyro_point + INS_GYRO_X_ADDRESS_OFFSET))))/3.14 * 180;
  
#if PITCH_TURN
  feedback_update->gimbal_pitch_motor.relative_angle = -motor_ecd_to_angle_change(feedback_update->gimbal_pitch_motor.gimbal_motor_measure->ecd,
                                                                                  feedback_update->gimbal_pitch_motor.offset_ecd);
#else

  feedback_update->gimbal_pitch_motor.relative_angle = motor_ecd_to_angle_change(feedback_update->gimbal_pitch_motor.gimbal_motor_measure->ecd,
                                                                                 feedback_update->gimbal_pitch_motor.offset_ecd);
#endif

  feedback_update->gimbal_pitch_motor.motor_gyro = *(feedback_update->gimbal_INT_gyro_point + INS_GYRO_Y_ADDRESS_OFFSET);

  feedback_update->gimbal_yaw_motor.absolute_angle = *(feedback_update->gimbal_INT_angle_point + INS_YAW_ADDRESS_OFFSET);

#if YAW_TURN
  feedback_update->gimbal_yaw_motor.relative_angle = -motor_ecd_to_angle_change(feedback_update->gimbal_yaw_motor.gimbal_motor_measure->ecd,
                                                                                feedback_update->gimbal_yaw_motor.offset_ecd);

#else
  feedback_update->gimbal_yaw_motor.relative_angle = motor_ecd_to_angle_change(feedback_update->gimbal_yaw_motor.gimbal_motor_measure->ecd,
                                                                               feedback_update->gimbal_yaw_motor.offset_ecd);
#endif
  feedback_update->gimbal_yaw_motor.motor_gyro = arm_cos_f32(feedback_update->gimbal_pitch_motor.relative_angle) * (*(feedback_update->gimbal_INT_gyro_point + INS_GYRO_Z_ADDRESS_OFFSET)) - arm_sin_f32(feedback_update->gimbal_pitch_motor.relative_angle) * (*(feedback_update->gimbal_INT_gyro_point + INS_GYRO_X_ADDRESS_OFFSET));
}

/**
 * @brief          ����ecd��offset_ecd֮�����ԽǶ�
 * @param[in]      ecd: �����ǰ����
 * @param[in]      offset_ecd: �����ֵ����
 * @retval         ��ԽǶȣ���λrad
 */
static fp32 motor_ecd_to_angle_change(uint16_t ecd, uint16_t offset_ecd)
{
  int32_t relative_ecd = ecd - offset_ecd;
  if (relative_ecd > HALF_ECD_RANGE)
  {
    relative_ecd -= ECD_RANGE;
  }
  else if (relative_ecd < -HALF_ECD_RANGE)
  {
    relative_ecd += ECD_RANGE;
  }
  return relative_ecd * MOTOR_ECD_TO_RAD;
}

/**
 * @brief  ����YAWƫ�����ĽǶ�,���̸���ģʽ��
 * @param  void
 * @retval sAngleError,ƫ��Ƕ�ֵ,CAN�����Ļ�е�Ƕ�
 */
uint8_t Multidirectional_FLAG;//45�ȳ�ǰ��־λ
float GIMBAL_GetOffsetAngle(void)
{
  Multidirectional_FLAG = FALSE;//45�ȳ�ǰ��־λ
  float sAngleError = 0;
  if((gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd > yaw_set_angle - 1024 && gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd < yaw_set_angle+1024)) 
      {
    gimbal_control.gimbal_yaw_motor.offset_ecd = yaw_set_angle;
    gimbal_control.chassis_move_direction = 0 ;
  }
  else if(gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd > yaw_set_angle+ 3072 && gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd < yaw_set_angle+ 5120)
  {
    gimbal_control.gimbal_yaw_motor.offset_ecd =  yaw_set_angle + 4096;
    gimbal_control.chassis_move_direction = 2 ;
  }
  else if(gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd >yaw_set_angle + 1024 && gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd < yaw_set_angle + 3072)
	{
    gimbal_control.gimbal_yaw_motor.offset_ecd =  yaw_set_angle + 2048;
    gimbal_control.chassis_move_direction = 1 ;
  }
	else if(gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd > yaw_set_angle + 5120 ||  gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd < yaw_set_angle - 1024 ) 
  {
    gimbal_control.gimbal_yaw_motor.offset_ecd =  yaw_set_angle + 6144;
    gimbal_control.chassis_move_direction = 3;
  }	
//	if(!IF_KEY_PRESSED_CTRL && IF_KEY_PRESSED_V  )
//	{
//			 if(gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd >yaw_set_angle-2048  && gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd < yaw_set_angle + 2048)
//		{
//			gimbal_control.gimbal_yaw_motor.offset_ecd =  yaw_set_angle ;
//			gimbal_control.chassis_move_direction = 0 ;
//		}
//	else  
//     {
//			gimbal_control.gimbal_yaw_motor.offset_ecd =  yaw_set_angle + 4096;
//			gimbal_control.chassis_move_direction = 2 ;
//		}		
//	}
		//��������µĸ���
	//1��
//	 if(toe_is_error(CHASSIS_MOTOR1_TOE))
//	{
//		if(gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd > 1773 && gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd < 5734)
//		{
//			gimbal_control.gimbal_yaw_motor.offset_ecd = yaw_set_angle-1024;
//			gimbal_control.chassis_move_direction = 0 ;  
//		}
//   else if((gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd >0 && gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd <1773 ) || (gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd >5734 && gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd <8192 ))
//		{
//			gimbal_control.gimbal_yaw_motor.offset_ecd = yaw_set_angle+3072;
//			gimbal_control.chassis_move_direction = 1 ;  
//		}
//	}	
	//2��
//	 if(toe_is_error(CHASSIS_MOTOR2_TOE))
//	{
//		if(gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd > 3527 && gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd < 7553)
//		{
//			gimbal_control.gimbal_yaw_motor.offset_ecd = yaw_set_angle+1024;
//			gimbal_control.chassis_move_direction = 0 ;  
//		}
//   else if((gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd >0 && gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd <3527 ) || (gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd >7553 && gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd <8192 ))
//		{
//			gimbal_control.gimbal_yaw_motor.offset_ecd = yaw_set_angle-3072;
//			gimbal_control.chassis_move_direction = 1 ;  
//		}
//	}	
	//3��
//	if(toe_is_error(CHASSIS_MOTOR3_TOE))
//	{
//		if(gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd > 1609 && gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd < 5643)
//		{
//			gimbal_control.gimbal_yaw_motor.offset_ecd = yaw_set_angle+2933;
//			gimbal_control.chassis_move_direction = 1 ;  
//		}
//   else if((gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd >0 && gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd <1609 ) || (gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd >5643 && gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd <8192 ))
//		{
//			gimbal_control.gimbal_yaw_motor.offset_ecd = yaw_set_angle+1172;
//			gimbal_control.chassis_move_direction = 0 ;  
//		}
//	}	
	//4��
//	if(toe_is_error(CHASSIS_MOTOR4_TOE))
//	{
//		if(gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd > 3667 && gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd < 7499)
//		{
//			gimbal_control.gimbal_yaw_motor.offset_ecd = yaw_set_angle-2979;
//			gimbal_control.chassis_move_direction = 1 ;  
//		}
//   else if((gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd >0 && gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd <3667 ) || (gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd >7499 && gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd <8192 ))
//		{
//			gimbal_control.gimbal_yaw_motor.offset_ecd = yaw_set_angle+1000;
//			gimbal_control.chassis_move_direction = 0 ;  
//		}
//	}
	
#if YAW_POSITION == YAW_DOWN
    sAngleError = gimbal_control.gimbal_yaw_motor.relative_angle ;
#else
    sAngleError = Cloud_Angle_Measure[YAW][CHANGE]; // CHANGE
#endif

  return sAngleError;
}


//���ӳ�ǰ
float chassia_Wheel_forward(void)
{ 
	  float sangleError = 0;
		if(gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd < yaw_set_angle+1024 
					&& gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd > yaw_set_angle-1024&&Multidirectional_FLAG==FALSE)
			{ 
				// ����ǰֹͣ���ǵøĶ�����
				gimbal_yaw_direction = GIMBAL_YAW_FORWARD_WHEEL;//����
				gimbal_control.gimbal_yaw_motor.offset_ecd = yaw_set_angle-1024;
				Multidirectional_FLAG=TRUE;
			}					
			else if(gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd < yaw_set_angle-3072 
					&& gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd > yaw_set_angle-5120&&Multidirectional_FLAG==FALSE)
			{
				gimbal_yaw_direction = GIMBAL_YAW_BACKWARD_WHEEL;
				gimbal_control.gimbal_yaw_motor.offset_ecd = yaw_set_angle-2048-1024;
				Multidirectional_FLAG=TRUE;

			}
			else if(gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd < yaw_set_angle-1024 
					&& gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd > yaw_set_angle-3072&&Multidirectional_FLAG==FALSE)
			{
				gimbal_yaw_direction = GIMBAL_YAW_RIGHT_WHEEL;
				gimbal_control.gimbal_yaw_motor.offset_ecd = yaw_set_angle-4096-1024;
				Multidirectional_FLAG=TRUE;
			}

			else if((gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd < 8192 
					&& gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd > yaw_set_angle+1024)||
					(gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd < yaw_set_angle-5120 
					&& gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd > 0&&Multidirectional_FLAG==FALSE))
			{ 
				// �����ֹͣ, �л�gimbal_yaw_direction
				gimbal_yaw_direction = GIMBAL_YAW_LIFT_WHEEL;
				gimbal_control.gimbal_yaw_motor.offset_ecd = yaw_set_angle+2048-1024;
				Multidirectional_FLAG=TRUE;
			}
			
#if YAW_POSITION == YAW_DOWN
  sangleError = gimbal_control.gimbal_yaw_motor.relative_angle;//��ԽǶ�
#else
  sanglerror = Cloud_Angle_Measure[YAW][CHANGE];; //yaw��ֵ
#endif
  
  return sangleError;

}
//45�ȳ�ǰ
float GIMBAL_Wheel_GetOffsetAngle(void)
{
  float sangleError = 0;
		if(gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd < yaw_set_angle+4096 
					&& gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd > yaw_set_angle+2048&&Multidirectional_FLAG==TRUE)
			{ 
				// ����ǰֹͣ���ǵøĶ�����
				gimbal_yaw_direction = GIMBAL_YAW_BACKWARD_WHEEL;//����
				gimbal_control.gimbal_yaw_motor.offset_ecd = yaw_set_angle+3096;
			}					
			else if(gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd < yaw_set_angle+6144 
					&& gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd > yaw_set_angle+4096&&Multidirectional_FLAG==TRUE)
			{
				gimbal_yaw_direction = GIMBAL_YAW_RIGHT_WHEEL;
				gimbal_control.gimbal_yaw_motor.offset_ecd = yaw_set_angle+4096+1024;

			}
			else if (((gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd < 8192 
					&&gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd > yaw_set_angle+6144)
                    ||(gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd < yaw_set_angle
                    && gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd >0))
                    &&Multidirectional_FLAG==TRUE)
			{
				gimbal_yaw_direction = GIMBAL_YAW_FORWARD_WHEEL;
				gimbal_control.gimbal_yaw_motor.offset_ecd = yaw_set_angle-1024;
			}

			else if(gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd < yaw_set_angle+2048
					&& gimbal_control.gimbal_yaw_motor.gimbal_motor_measure->ecd > yaw_set_angle&&Multidirectional_FLAG==TRUE)
			{ 
				// �����ֹͣ, �л�gimbal_yaw_direction
				gimbal_yaw_direction = GIMBAL_YAW_LIFT_WHEEL;
				gimbal_control.gimbal_yaw_motor.offset_ecd = yaw_set_angle+1024;
			}
		
#if YAW_POSITION == YAW_DOWN
  sangleError = gimbal_control.gimbal_yaw_motor.relative_angle;//��ԽǶ�
#else
  sanglerror = Cloud_Angle_Measure[YAW][CHANGE];; //yaw��ֵ
#endif
  
  return sangleError;
}



uint8_t chassis_move(void)
{
  return gimbal_control.chassis_move_direction; 
}

/**
 * @brief          ��̨ģʽ�ı䣬��Щ������Ҫ�ı䣬�������yaw�Ƕ��趨ֵӦ�ñ�ɵ�ǰyaw�Ƕ�
 * @param[out]     gimbal_mode_change:"gimbal_control"����ָ��.
 * @retval         none
 */
static void gimbal_mode_change_control_transit(gimbal_control_t *gimbal_mode_change)
{
  if (gimbal_mode_change == NULL)
  {
    return;
  }
  // yaw���״̬���л���������
  if (gimbal_mode_change->gimbal_yaw_motor.last_gimbal_motor_mode != GIMBAL_MOTOR_RAW && gimbal_mode_change->gimbal_yaw_motor.gimbal_motor_mode == GIMBAL_MOTOR_RAW)
  {
    gimbal_mode_change->gimbal_yaw_motor.raw_cmd_current = gimbal_mode_change->gimbal_yaw_motor.current_set = gimbal_mode_change->gimbal_yaw_motor.given_current;
  }
  else if (gimbal_mode_change->gimbal_yaw_motor.last_gimbal_motor_mode != GIMBAL_MOTOR_GYRO && gimbal_mode_change->gimbal_yaw_motor.gimbal_motor_mode == GIMBAL_MOTOR_GYRO)
  {
    gimbal_mode_change->gimbal_yaw_motor.absolute_angle_set = gimbal_mode_change->gimbal_yaw_motor.absolute_angle;
  }
  else if (gimbal_mode_change->gimbal_yaw_motor.last_gimbal_motor_mode != GIMBAL_MOTOR_ENCONDE && gimbal_mode_change->gimbal_yaw_motor.gimbal_motor_mode == GIMBAL_MOTOR_ENCONDE)
  {
    gimbal_mode_change->gimbal_yaw_motor.relative_angle_set = gimbal_mode_change->gimbal_yaw_motor.relative_angle;
  }
  else if (gimbal_mode_change->gimbal_yaw_motor.last_gimbal_motor_mode != GIMBAL_MOTOR_SPIN && gimbal_mode_change->gimbal_yaw_motor.gimbal_motor_mode == GIMBAL_MOTOR_SPIN)
  {
    gimbal_mode_change->gimbal_yaw_motor.absolute_angle_set = gimbal_mode_change->gimbal_yaw_motor.absolute_angle;
  }
  gimbal_mode_change->gimbal_yaw_motor.last_gimbal_motor_mode = gimbal_mode_change->gimbal_yaw_motor.gimbal_motor_mode;

  // pitch���״̬���л���������
  if (gimbal_mode_change->gimbal_pitch_motor.last_gimbal_motor_mode != GIMBAL_MOTOR_RAW && gimbal_mode_change->gimbal_pitch_motor.gimbal_motor_mode == GIMBAL_MOTOR_RAW)
  {
    gimbal_mode_change->gimbal_pitch_motor.raw_cmd_current = gimbal_mode_change->gimbal_pitch_motor.current_set = gimbal_mode_change->gimbal_pitch_motor.given_current;
  }
  else if (gimbal_mode_change->gimbal_pitch_motor.last_gimbal_motor_mode != GIMBAL_MOTOR_GYRO && gimbal_mode_change->gimbal_pitch_motor.gimbal_motor_mode == GIMBAL_MOTOR_GYRO)
  {
    gimbal_mode_change->gimbal_pitch_motor.absolute_angle_set = gimbal_mode_change->gimbal_pitch_motor.absolute_angle;
  }
  else if (gimbal_mode_change->gimbal_pitch_motor.last_gimbal_motor_mode != GIMBAL_MOTOR_ENCONDE && gimbal_mode_change->gimbal_pitch_motor.gimbal_motor_mode == GIMBAL_MOTOR_ENCONDE)
  {
    gimbal_mode_change->gimbal_pitch_motor.relative_angle_set = gimbal_mode_change->gimbal_pitch_motor.relative_angle;
  }

  gimbal_mode_change->gimbal_pitch_motor.last_gimbal_motor_mode = gimbal_mode_change->gimbal_pitch_motor.gimbal_motor_mode;
}
/**
 * @brief          ������̨�����趨ֵ������ֵ��ͨ��gimbal_behaviour_control_set�������õ�
 * @param[out]     gimbal_set_control:"gimbal_control"����ָ��.
 * @retval         none
 */

static void gimbal_set_control(gimbal_control_t *set_control)
{
  static fp32 bias_angle;
  static fp32 angle_set;
  if (set_control == NULL)
  {
    return;
  }

  fp32 add_yaw_angle = 0.0f;
  fp32 add_pitch_angle = 0.0f;
  if (set_control->gimbal_rc_ctrl->mouse.press_r)
  {
    GIMBAL_AUTO_Mode_Ctrl(set_control);
    gimbal_control.auto_mode = TRUE;
  }
  else
  {
    gimbal_control.auto_mode = FALSE;
  }
  if( set_control->gimbal_rc_ctrl->key.v & KEY_PRESSED_OFFSET_B)
  {
     gimbal_control.gimbal_pitch_motor.absolute_angle_set = 0;
  }
  // #ifdef VISION_TEST
  // if(set_control->gimbal_rc_ctrl->rc.s[1] == 3 && set_control->gimbal_rc_ctrl->rc.s[0] == 3)
  // {
  //   GIMBAL_AUTO_Mode_Ctrl(set_control);
  // }
  // #endif
  //#ifdef VISION_TEST
  //    GIMBAL_AUTO_Mode_Ctrl(set_control);
  //#else
  //    if(set_control->gimbal_rc_ctrl->mouse.press_r!=set_control->gimbal_rc_ctrl->mouse.press_r_last)
  //		{
  //			set_control->gimbal_pitch_motor.absolute_angle_set = set_control->gimbal_pitch_motor.absolute_angle;
  //			set_control->gimbal_yaw_motor.absolute_angle_set = set_control->gimbal_yaw_motor.absolute_angle;
  //		}
  //		if(set_control->gimbal_rc_ctrl->mouse.press_r == 1)
  //		{
  //			GIMBAL_AUTO_Mode_Ctrl(set_control);
  //		}
  //		else
  //		{
  //			//ң�������̵�����
  //      gimbal_behaviour_control_set(&add_yaw_angle, &add_pitch_angle,set_control);
  //		}
  //#endif

  gimbal_behaviour_control_set(&add_yaw_angle, &add_pitch_angle, set_control);
  // yaw���ģʽ����
  if (set_control->gimbal_yaw_motor.gimbal_motor_mode == GIMBAL_MOTOR_RAW)
  {
    // rawģʽ�£�ֱ�ӷ��Ϳ���ֵ
    set_control->gimbal_yaw_motor.raw_cmd_current = add_yaw_angle;
  }
  else if (set_control->gimbal_yaw_motor.gimbal_motor_mode == GIMBAL_MOTOR_GYRO)
  {
    // gyroģʽ�£������ǽǶȿ���
    gimbal_absolute_angle_limit(&set_control->gimbal_yaw_motor, add_yaw_angle);
  }
  else if (set_control->gimbal_yaw_motor.gimbal_motor_mode == GIMBAL_MOTOR_ENCONDE)
  {
    // encondeģʽ�£��������Ƕȿ���
    gimbal_relative_angle_limit(&set_control->gimbal_yaw_motor, add_yaw_angle);
  }
  else if (set_control->gimbal_yaw_motor.gimbal_motor_mode == GIMBAL_MOTOR_SPIN)
  {
    //С����ģʽ�£�ͨ�������ǽǿ��ƣ�ͬʱȡ����λ
      gimbal_absolute_angle_limit(&set_control->gimbal_yaw_motor, add_yaw_angle);
   // set_control->gimbal_yaw_motor.absolute_angle_set = set_control->gimbal_yaw_motor.absolute_angle_set + add_yaw_angle;
  }
  //  else if (set_control->gimbal_yaw_motor.gimbal_motor_mode == GIMBAL_MOTOR_AUTO)

  //************** pitch���ģʽ����****************************************
  if (set_control->gimbal_pitch_motor.gimbal_motor_mode == GIMBAL_MOTOR_RAW)
  {
    // rawģʽ�£�ֱ�ӷ��Ϳ���ֵ
    set_control->gimbal_pitch_motor.raw_cmd_current = add_pitch_angle;
  }
  else if (set_control->gimbal_pitch_motor.gimbal_motor_mode == GIMBAL_MOTOR_GYRO)
  {
    // gyroģʽ�£������ǽǶȿ���
    gimbal_absolute_angle_limit(&set_control->gimbal_pitch_motor, add_pitch_angle);
  }
  else if (set_control->gimbal_pitch_motor.gimbal_motor_mode == GIMBAL_MOTOR_ENCONDE)
  {
    // encondeģʽ�£��������Ƕȿ���
    gimbal_relative_angle_limit(&set_control->gimbal_pitch_motor, add_pitch_angle);
  }
}
/**
 * @brief          ��̨����ģʽ:GIMBAL_MOTOR_GYRO��ʹ�������Ǽ����ŷ���ǽ��п���
 * @param[out]     gimbal_motor:yaw�������pitch���
 * @retval         none
 */
static void gimbal_absolute_angle_limit(gimbal_motor_t *gimbal_motor, fp32 add)
{
  static fp32 bias_angle;
  static fp32 angle_set;
  if (gimbal_motor == NULL)
  {
    return;
  }
  // now angle error
  //��ǰ�������Ƕ�
  bias_angle = rad_format(gimbal_motor->absolute_angle_set - gimbal_motor->absolute_angle);

  // relative angle + angle error + add_angle > max_relative angle
  //��̨��ԽǶ�+ ���Ƕ� + �����Ƕ� ������� ����е�Ƕ�
  if (gimbal_motor->gimbal_motor_mode == GIMBAL_MOTOR_SPIN)
  {
    angle_set = gimbal_motor->absolute_angle_set;
    gimbal_motor->absolute_angle_set = rad_format(angle_set + add);
    return;
  }
  else
  {
//    angle_set = gimbal_motor->absolute_angle_set;
//    gimbal_motor->absolute_angle_set = rad_format(angle_set + add);
    //һ��Ϊ������λ
    if (gimbal_motor->relative_angle + bias_angle + add > gimbal_motor->max_relative_angle)
    {
      //�����������е�Ƕȿ��Ʒ���
      if (add > 0.0f)
      {
        // calculate max add_angle
        //�����һ���������ӽǶ�
        add = gimbal_motor->max_relative_angle - gimbal_motor->relative_angle - bias_angle;
      }
    }
    else if (gimbal_motor->relative_angle + bias_angle + add < gimbal_motor->min_relative_angle)
    {
      if (add < 0.0f)
      {
        add = gimbal_motor->min_relative_angle - gimbal_motor->relative_angle - bias_angle;
      }
    }
  }
  angle_set = gimbal_motor->absolute_angle_set;
  gimbal_motor->absolute_angle_set = rad_format(angle_set + add);
}
/**
 * @brief          ��̨����ģʽ:GIMBAL_MOTOR_ENCONDE��ʹ�ñ�����Խǽ��п���
 * @param[out]     gimbal_motor:yaw�������pitch���
 * @retval         none
 */
static void gimbal_relative_angle_limit(gimbal_motor_t *gimbal_motor, fp32 add)
{
  if (gimbal_motor == NULL)
  {
    return;
  }
  gimbal_motor->relative_angle_set += add;
  //�Ƿ񳬹���� ��Сֵ
  if (gimbal_motor->relative_angle_set > gimbal_motor->max_relative_angle)
  {
    gimbal_motor->relative_angle_set = gimbal_motor->max_relative_angle;
  }
  else if (gimbal_motor->relative_angle_set < gimbal_motor->min_relative_angle)
  {
    gimbal_motor->relative_angle_set = gimbal_motor->min_relative_angle;
  }
}
/**
 * @brief          ����ѭ�������ݿ����趨ֵ������������ֵ�����п���
 * @param[out]     gimbal_control_loop:"gimbal_control"����ָ��.
 * @retval         none
 */
static void gimbal_control_loop(gimbal_control_t *control_loop)
{
  if (control_loop == NULL)
  {
    return;
  }

  if (control_loop->gimbal_yaw_motor.gimbal_motor_mode == GIMBAL_MOTOR_RAW)
  {
    gimbal_motor_raw_angle_control(&control_loop->gimbal_yaw_motor);
  }
  else if (control_loop->gimbal_yaw_motor.gimbal_motor_mode == GIMBAL_MOTOR_GYRO)
  {
    //�ǶȻ����ٶȻ�����pid����										                              	PID�����ṹ��									                               �趨ֵ			                                   ʵ��ֵ������ԽǶ�
    control_loop->gimbal_yaw_motor.motor_gyro_set = New_PID_cail(&GIRT_yaw_tuoluo_pid, control_loop->gimbal_yaw_motor.absolute_angle_set, control_loop->gimbal_yaw_motor.absolute_angle, LOOP_ERR);
    //�ٶȻ�																		                     			PID�����ṹ��						                          		�趨ֵ                                         ʵ��ֵ�������ٶ�
    control_loop->gimbal_yaw_motor.current_set = New_PID_cail(&GIRT_yaw_s_pid, control_loop->gimbal_yaw_motor.motor_gyro_set, control_loop->gimbal_yaw_motor.motor_gyro, LINE_ERR);
		
//	  //�ǶȻ����ٶȻ�����pid����										                              	PID�����ṹ��									                               �趨ֵ			                                   ʵ��ֵ������ԽǶ�
//    control_loop->gimbal_yaw_motor.motor_gyro_set = Class_PID_TIM_Adjust_PeriodElapsedCallback(&GIRT_yaw_tuoluo_pid, control_loop->gimbal_yaw_motor.absolute_angle_set, control_loop->gimbal_yaw_motor.absolute_angle);
//    //�ٶȻ�																		                     			PID�����ṹ��						                          		�趨ֵ                                         ʵ��ֵ�������ٶ�
//    control_loop->gimbal_yaw_motor.current_set = Class_PID_TIM_Adjust_PeriodElapsedCallback(&GIRT_yaw_s_pid, control_loop->gimbal_yaw_motor.motor_gyro_set, control_loop->gimbal_yaw_motor.motor_gyro);
    //����ֵ��ֵ
    control_loop->gimbal_yaw_motor.given_current = (int16_t)(control_loop->gimbal_yaw_motor.current_set);
    // gimbal_motor_absolute_angle_control(&control_loop->gimbal_yaw_motor);
  }
  else if (control_loop->gimbal_yaw_motor.gimbal_motor_mode == GIMBAL_MOTOR_ENCONDE)
  {
    //�ٶȻ�																		                     			PID�����ṹ��						                          		�趨ֵ                                         ʵ��ֵ�������ٶ�
    control_loop->gimbal_yaw_motor.current_set = New_PID_cail(&GIRT_yaw_s_pid, control_loop->gimbal_yaw_motor.motor_gyro_set, control_loop->gimbal_yaw_motor.motor_gyro, LINE_ERR);
		
//		  //�ٶȻ�																		                     			PID�����ṹ��						                          		�趨ֵ                                         ʵ��ֵ�������ٶ�
//    control_loop->gimbal_yaw_motor.current_set = Class_PID_TIM_Adjust_PeriodElapsedCallback(&GIRT_yaw_s_pid, control_loop->gimbal_yaw_motor.motor_gyro_set, control_loop->gimbal_yaw_motor.motor_gyro);
    //����ֵ��ֵ
    control_loop->gimbal_yaw_motor.given_current = (int16_t)(control_loop->gimbal_yaw_motor.current_set);
  }
  else if (control_loop->gimbal_yaw_motor.gimbal_motor_mode == GIMBAL_MOTOR_SPIN)
  {
    if (control_loop->auto_mode == TRUE)
    {
			if(IF_KEY_PRESSED_F)
			{
				control_loop->gimbal_yaw_motor.motor_gyro_set = New_PID_cail(&GIRT_buff_yaw_auto_pid, control_loop->gimbal_yaw_motor.absolute_angle_set, control_loop->gimbal_yaw_motor.absolute_angle,LOOP_ERR);
			  control_loop->gimbal_yaw_motor.current_set = New_PID_cail(&GIRT_buff_auto_yaw_speed_pid, control_loop->gimbal_yaw_motor.motor_gyro_set, control_loop->gimbal_yaw_motor.motor_gyro,LINE_ERR);   

			}
			else if(!IF_KEY_PRESSED_F)
			{
      //�ǶȻ����ٶȻ�����pid����	
				control_loop->gimbal_yaw_motor.motor_gyro_set = New_PID_cail(&GIRT_yaw_auto_pid, control_loop->gimbal_yaw_motor.absolute_angle_set, control_loop->gimbal_yaw_motor.absolute_angle, LOOP_ERR);
				//�ٶȻ�															
				control_loop->gimbal_yaw_motor.current_set = New_PID_cail(&GIRT_yaw_auto_speed_pid, control_loop->gimbal_yaw_motor.motor_gyro_set, control_loop->gimbal_yaw_motor.motor_gyro, LINE_ERR);   
				
//			//�ǶȻ����ٶȻ�����pid����	
//      control_loop->gimbal_yaw_motor.motor_gyro_set = Class_PID_TIM_Adjust_PeriodElapsedCallback(&GIRT_yaw_auto_pid, control_loop->gimbal_yaw_motor.absolute_angle_set, control_loop->gimbal_yaw_motor.absolute_angle);
//      //�ٶȻ�															
//      control_loop->gimbal_yaw_motor.current_set = Class_PID_TIM_Adjust_PeriodElapsedCallback(&GIRT_yaw_auto_speed_pid, control_loop->gimbal_yaw_motor.motor_gyro_set, control_loop->gimbal_yaw_motor.motor_gyro);   
			}
    }
    else
    {
      //�ǶȻ�	
			control_loop->gimbal_yaw_motor.motor_gyro_set = New_PID_cail(&GIRT_yaw_tuoluo_pid, control_loop->gimbal_yaw_motor.absolute_angle_set, control_loop->gimbal_yaw_motor.absolute_angle, LOOP_ERR);
      //�ٶȻ�						             
      control_loop->gimbal_yaw_motor.current_set = New_PID_cail(&GIRT_yaw_s_pid, control_loop->gimbal_yaw_motor.motor_gyro_set, control_loop->gimbal_yaw_motor.motor_gyro, LINE_ERR);   
			
//			//�ǶȻ�	
//      control_loop->gimbal_yaw_motor.motor_gyro_set = Class_PID_TIM_Adjust_PeriodElapsedCallback(&GIRT_yaw_tuoluo_pid, control_loop->gimbal_yaw_motor.absolute_angle_set, control_loop->gimbal_yaw_motor.absolute_angle);
//      //�ٶȻ�						             
//      control_loop->gimbal_yaw_motor.current_set = Class_PID_TIM_Adjust_PeriodElapsedCallback(&GIRT_yaw_s_pid, control_loop->gimbal_yaw_motor.motor_gyro_set, control_loop->gimbal_yaw_motor.motor_gyro);   
    }
    //����ֵ��ֵ
    control_loop->gimbal_yaw_motor.given_current = (int16_t)(control_loop->gimbal_yaw_motor.current_set);
  }




  if (control_loop->gimbal_pitch_motor.gimbal_motor_mode == GIMBAL_MOTOR_RAW)
  {
    gimbal_motor_raw_angle_control(&control_loop->gimbal_pitch_motor);
  }
  else if (control_loop->gimbal_pitch_motor.gimbal_motor_mode == GIMBAL_MOTOR_GYRO)
  {
      if (control_loop->auto_mode == TRUE)
			{
				if(IF_KEY_PRESSED_F)
				{
						//����ǶȻ�
					control_loop->gimbal_pitch_motor.motor_gyro_set = New_PID_cail(&GIRT_buff_pitch_auto_pid, control_loop->gimbal_pitch_motor.absolute_angle_set, control_loop->gimbal_pitch_motor.absolute_angle,LOOP_ERR);
					//�����ٶȻ�
					control_loop->gimbal_pitch_motor.current_set = New_PID_cail(&GIRT_buff_auto_pitch_speed_pid, control_loop->gimbal_pitch_motor.motor_gyro_set, control_loop->gimbal_pitch_motor.motor_gyro,LINE_ERR);    
				}
				else
				{
					//����ǶȻ�
					control_loop->gimbal_pitch_motor.motor_gyro_set = New_PID_cail(&GIRT_pitch_auto_pid, control_loop->gimbal_pitch_motor.absolute_angle_set, control_loop->gimbal_pitch_motor.absolute_angle, LOOP_ERR);
					//�����ٶȻ�
					control_loop->gimbal_pitch_motor.current_set = New_PID_cail(&GIRT_pitch_auto_speed_pid, control_loop->gimbal_pitch_motor.motor_gyro_set, control_loop->gimbal_pitch_motor.motor_gyro, LINE_ERR);    						
				}
		
				//����ǶȻ�
	//      control_loop->gimbal_pitch_motor.motor_gyro_set = Class_PID_TIM_Adjust_PeriodElapsedCallback(&GIRT_pitch_auto_pid, control_loop->gimbal_pitch_motor.absolute_angle_set, control_loop->gimbal_pitch_motor.absolute_angle);
	//      //�����ٶȻ�
	//      control_loop->gimbal_pitch_motor.current_set = Class_PID_TIM_Adjust_PeriodElapsedCallback(&GIRT_pitch_auto_speed_pid, control_loop->gimbal_pitch_motor.motor_gyro_set, control_loop->gimbal_pitch_motor.motor_gyro);    
			}    
    //  gimbal_motor_absolute_angle_control(&control_loop->gimbal_pitch_motor);
    else
    {
      //�ǶȻ�
      control_loop->gimbal_pitch_motor.motor_gyro_set = New_PID_cail(&GIRT_pitch_encode_pid, control_loop->gimbal_pitch_motor.absolute_angle_set, control_loop->gimbal_pitch_motor.absolute_angle, LOOP_ERR);
      //�ٶȻ�
      control_loop->gimbal_pitch_motor.current_set = New_PID_cail(&GIRT_pitch_s_pid, control_loop->gimbal_pitch_motor.motor_gyro_set, control_loop->gimbal_pitch_motor.motor_gyro, LINE_ERR);
			
//			//�ǶȻ�
//      control_loop->gimbal_pitch_motor.motor_gyro_set = Class_PID_TIM_Adjust_PeriodElapsedCallback(&GIRT_pitch_encode_pid, control_loop->gimbal_pitch_motor.absolute_angle_set, control_loop->gimbal_pitch_motor.absolute_angle);
//      //�ٶȻ�
//      control_loop->gimbal_pitch_motor.current_set = Class_PID_TIM_Adjust_PeriodElapsedCallback(&GIRT_pitch_s_pid, control_loop->gimbal_pitch_motor.motor_gyro_set, control_loop->gimbal_pitch_motor.motor_gyro);
    }
    //����ֵ��ֵ
    control_loop->gimbal_pitch_motor.given_current = (int16_t)(control_loop->gimbal_pitch_motor.current_set);
  }
  else if (control_loop->gimbal_pitch_motor.gimbal_motor_mode == GIMBAL_MOTOR_ENCONDE)
  {
    if (control_loop->auto_mode == TRUE)
    {
      //����ǶȻ�	
      control_loop->gimbal_pitch_motor.motor_gyro_set = New_PID_cail(&GIRT_pitch_auto_pid, control_loop->gimbal_pitch_motor.relative_angle_set, control_loop->gimbal_pitch_motor.relative_angle, LOOP_ERR);
      //�����ٶȻ�    			                     			PID�����ṹ��						                          		�趨ֵ                                         ʵ��ֵ�������ٶ�
      control_loop->gimbal_pitch_motor.current_set = New_PID_cail(&GIRT_pitch_auto_speed_pid, control_loop->gimbal_pitch_motor.motor_gyro_set, control_loop->gimbal_pitch_motor.motor_gyro, LINE_ERR);    
			
			//����ǶȻ�	
//      control_loop->gimbal_pitch_motor.motor_gyro_set = Class_PID_TIM_Adjust_PeriodElapsedCallback(&GIRT_pitch_auto_pid, control_loop->gimbal_pitch_motor.relative_angle_set, control_loop->gimbal_pitch_motor.relative_angle);
//      //�����ٶȻ�																		                     	                     																	                     			PID�����ṹ��						                          		�趨ֵ                                         ʵ��ֵ�������ٶ�
//      control_loop->gimbal_pitch_motor.current_set = Class_PID_TIM_Adjust_PeriodElapsedCallback(&GIRT_pitch_auto_speed_pid, control_loop->gimbal_pitch_motor.motor_gyro_set, control_loop->gimbal_pitch_motor.motor_gyro);    

    }
    else
    {
      //�ǶȻ����ٶȻ�����pid����										                              	PID�����ṹ��									                               �趨ֵ			                                   ʵ��ֵ������ԽǶ�
      control_loop->gimbal_pitch_motor.motor_gyro_set = New_PID_cail(&GIRT_pitch_encode_pid, control_loop->gimbal_pitch_motor.relative_angle_set, control_loop->gimbal_pitch_motor.relative_angle, LOOP_ERR);
      //�ٶȻ�																		                     			PID�����ṹ��						                          		�趨ֵ                                         ʵ��ֵ�������ٶ�
      control_loop->gimbal_pitch_motor.current_set = New_PID_cail(&GIRT_pitch_s_pid, control_loop->gimbal_pitch_motor.motor_gyro_set, control_loop->gimbal_pitch_motor.motor_gyro, LINE_ERR);    
			
//			//�ǶȻ����ٶȻ�����pid����										                              	PID�����ṹ��									                               �趨ֵ			                                   ʵ��ֵ������ԽǶ�
//      control_loop->gimbal_pitch_motor.motor_gyro_set = Class_PID_TIM_Adjust_PeriodElapsedCallback(&GIRT_pitch_encode_pid, control_loop->gimbal_pitch_motor.relative_angle_set, control_loop->gimbal_pitch_motor.relative_angle);
//      //�ٶȻ�																		                     			PID�����ṹ��						                          		�趨ֵ                                         ʵ��ֵ�������ٶ�
//      control_loop->gimbal_pitch_motor.current_set = Class_PID_TIM_Adjust_PeriodElapsedCallback(&GIRT_pitch_s_pid, control_loop->gimbal_pitch_motor.motor_gyro_set, control_loop->gimbal_pitch_motor.motor_gyro);
    }
   //����ֵ��ֵ
    control_loop->gimbal_pitch_motor.given_current = (int16_t)(control_loop->gimbal_pitch_motor.current_set);
  }
}
/**
 * @brief          ��̨����ģʽ:GIMBAL_MOTOR_GYRO��ʹ�������Ǽ����ŷ���ǽ��п���
 * @param[out]     gimbal_motor:yaw�������pitch���
 * @retval         none
 */
static void gimbal_motor_absolute_angle_control(gimbal_motor_t *gimbal_motor)
{
  if (gimbal_motor == NULL)
  {
    return;
  }
  //�ǶȻ����ٶȻ�����pid����
  gimbal_motor->motor_gyro_set = gimbal_PID_calc(&gimbal_motor->gimbal_motor_absolute_angle_pid, gimbal_motor->absolute_angle, gimbal_motor->absolute_angle_set, gimbal_motor->motor_gyro);
  gimbal_motor->current_set = PID_calc(&gimbal_motor->gimbal_motor_gyro_pid, gimbal_motor->motor_gyro, gimbal_motor->motor_gyro_set);
  //����ֵ��ֵ
  gimbal_motor->given_current = (int16_t)(gimbal_motor->current_set);
}
/**
 * @brief          ��̨����ģʽ:GIMBAL_MOTOR_ENCONDE��ʹ�ñ�����Խǽ��п���
 * @param[out]     gimbal_motor:yaw�������pitch���
 * @retval         none
 */
static void gimbal_motor_relative_angle_control(gimbal_motor_t *gimbal_motor)
{
  if (gimbal_motor == NULL)
  {
    return;
  }

  //�ǶȻ����ٶȻ�����pid����
  gimbal_motor->motor_gyro_set = gimbal_PID_calc(&gimbal_motor->gimbal_motor_relative_angle_pid, gimbal_motor->relative_angle, gimbal_motor->relative_angle_set, gimbal_motor->motor_gyro);
  gimbal_motor->current_set = PID_calc(&gimbal_motor->gimbal_motor_gyro_pid, gimbal_motor->motor_gyro, gimbal_motor->motor_gyro_set);
  //����ֵ��ֵ
  gimbal_motor->given_current = (int16_t)(gimbal_motor->current_set);
}

/**
 * @brief          ��̨����ģʽ:GIMBAL_MOTOR_RAW������ֱֵ�ӷ��͵�CAN����.
 * @param[out]     gimbal_motor:yaw�������pitch���
 * @retval         none
 */
static void gimbal_motor_raw_angle_control(gimbal_motor_t *gimbal_motor)
{
  if (gimbal_motor == NULL)
  {
    return;
  }
  gimbal_motor->current_set = gimbal_motor->raw_cmd_current;
  gimbal_motor->given_current = (int16_t)(gimbal_motor->current_set);
}

#if GIMBAL_TEST_MODE

int32_t pitch_relative_set_1000, pitch_relative_angle_1000;
 
int32_t yaw_ins_int_1000, pitch_ins_int_1000;
int32_t yaw_ins_set_1000,pitch_ins_set_1000;
int32_t yaw_speed_int_1000, pitch_speed_int_1000;
int32_t yaw_speed_set_int_1000, pitch_speed_set_int_1000;
static void J_scope_gimbal_test(void)
{
  yaw_ins_int_1000 = (int32_t)(gimbal_control.gimbal_yaw_motor.absolute_angle * 1000);
  yaw_ins_set_1000 = (int32_t)(gimbal_control.gimbal_yaw_motor.absolute_angle_set * 1000);
  yaw_speed_int_1000 = (int32_t)(gimbal_control.gimbal_yaw_motor.motor_gyro * 1000);
  yaw_speed_set_int_1000 = (int32_t)(gimbal_control.gimbal_yaw_motor.motor_gyro_set * 1000);

  pitch_ins_int_1000 = (int32_t)(gimbal_control.gimbal_pitch_motor.absolute_angle * 1000);
  pitch_ins_set_1000 = (int32_t)(gimbal_control.gimbal_pitch_motor.absolute_angle_set * 1000);
  pitch_speed_int_1000 = (int32_t)(gimbal_control.gimbal_pitch_motor.motor_gyro * 1000);
  pitch_speed_set_int_1000 = (int32_t)(gimbal_control.gimbal_pitch_motor.motor_gyro_set * 1000);
  pitch_relative_angle_1000 = (int32_t)(gimbal_control.gimbal_pitch_motor.relative_angle * 1000);
  pitch_relative_set_1000 = (int32_t)(gimbal_control.gimbal_pitch_motor.relative_angle_set * 1000);
}

#endif

/**
 * @brief          "gimbal_control" valiable initialization, include pid initialization, remote control data point initialization, gimbal motors
 *                 data point initialization, and gyro sensor angle point initialization.
 * @param[out]     gimbal_init: "gimbal_control" valiable point
 * @retval         none
 */
/**
 * @brief          ��ʼ��"gimbal_control"����������pid��ʼ���� ң����ָ���ʼ������̨���ָ���ʼ���������ǽǶ�ָ���ʼ��
 * @param[out]     gimbal_init:"gimbal_control"����ָ��.
 * @retval         none
 */
static void gimbal_PID_init(gimbal_PID_t *pid, fp32 maxout, fp32 max_iout, fp32 kp, fp32 ki, fp32 kd)
{
  if (pid == NULL)
  {
    return;
  }
  pid->kp = kp;
  pid->ki = ki;
  pid->kd = kd;

  pid->err = 0.0f;
  pid->get = 0.0f;

  pid->max_iout = max_iout;
  pid->max_out = maxout;
}

static fp32 gimbal_PID_calc(gimbal_PID_t *pid, fp32 get, fp32 set, fp32 error_delta)
{
  fp32 err;
  if (pid == NULL)
  {
    return 0.0f;
  }
  pid->get = get;
  pid->set = set;

  err = set - get;
  pid->err = rad_format(err);
  pid->Pout = pid->kp * pid->err;
  pid->Iout += pid->ki * pid->err;
  pid->Dout = pid->kd * error_delta;
  abs_limit(&pid->Iout, pid->max_iout);
  pid->out = pid->Pout + pid->Iout + pid->Dout;
  abs_limit(&pid->out, pid->max_out);
  return pid->out;
}

/**
 * @brief          ��̨PID��������pid��out,iout
 * @param[out]     gimb    al_pid_clear:"gimbal_control"����ָ��.
 * @retval         none
 */
static void gimbal_PID_clear(gimbal_PID_t *gimbal_pid_clear)
{
  if (gimbal_pid_clear == NULL)
  {
    return;
  }
  gimbal_pid_clear->err = gimbal_pid_clear->set = gimbal_pid_clear->get = 0.0f;
  gimbal_pid_clear->out = gimbal_pid_clear->Pout = gimbal_pid_clear->Iout = gimbal_pid_clear->Dout = 0.0f;
}

void GIMBAL_AUTO_Mode_Ctrl(gimbal_control_t *gimbal_auto_control)
{
	yaw_angle_ref_last=yaw_angle_ref;
  //��ȡ�Ƕ�ƫ����,ŷ��������,�����������Ӿ��ľ�׼��
  Vision_Error_Angle_YAW(&yaw_angle_ref);
  Vision_Error_Angle_PITCH(&pitch_angle_ref);
  vision_get = Vision_If_UpDate();
//	if(fabs(yaw_angle_ref-yaw_angle_ref_last)>=1.5f)
//	{
//		yaw_angle_ref=yaw_angle_ref_last;
//	}
  //	vision_get_Distance(&(gimbal_auto_control->gimbal_kalman.Auto_Distance));
  //���������������ǶԴ�������ĸ�ֵ�������൱�����ⲿ�����ṩ�ӿ�
  if (vision_get)
  {
    // gimbal_control.gimbal_pitch_motor.relative_angle_set = pitch_angle_ref;
    //    gimbal_control.gimbal_yaw_motor.relative_angle_set = yaw_angle_ref;
    gimbal_control.gimbal_pitch_motor.absolute_angle_set = pitch_angle_ref;  //����
    gimbal_control.gimbal_yaw_motor.absolute_angle_set = yaw_angle_ref;
  }

}
