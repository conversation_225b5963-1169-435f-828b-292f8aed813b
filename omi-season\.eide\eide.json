{"name": "VESTA", "type": "ARM", "dependenceList": [], "srcDirs": [], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": [{"name": "Application", "files": [], "folders": [{"name": "MDK-ARM", "files": [{"path": "MDK-ARM/startup_stm32f407xx.s"}], "folders": []}, {"name": "User", "files": [{"path": "application/kalman.c"}, {"path": "Src/main.c"}, {"path": "Src/gpio.c"}, {"path": "Src/freertos.c"}, {"path": "Src/adc.c"}, {"path": "Src/can.c"}, {"path": "Src/crc.c"}, {"path": "Src/dma.c"}, {"path": "Src/i2c.c"}, {"path": "Src/rng.c"}, {"path": "Src/rtc.c"}, {"path": "Src/spi.c"}, {"path": "Src/tim.c"}, {"path": "Src/usart.c"}, {"path": "Src/usb_device.c"}, {"path": "Src/usbd_conf.c"}, {"path": "Src/usbd_desc.c"}, {"path": "Src/usbd_cdc_if.c"}, {"path": "Src/stm32f4xx_it.c"}, {"path": "Src/stm32f4xx_hal_msp.c"}, {"path": "Src/pm01_api.c"}], "folders": []}]}, {"name": "Drivers", "files": [], "folders": [{"name": "CMSIS", "files": [{"path": "Src/system_stm32f4xx.c"}], "folders": []}, {"name": "STM32F4xx_HAL_Driver", "files": [{"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pcd.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pcd_ex.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_usb.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_can.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_crc.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rng.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rtc.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rtc_ex.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c"}, {"path": "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c"}], "folders": []}]}, {"name": "boards", "files": [{"path": "bsp/boards/bsp_adc.c"}, {"path": "bsp/boards/bsp_buzzer.c"}, {"path": "bsp/boards/bsp_can.c"}, {"path": "bsp/boards/bsp_crc32.c"}, {"path": "bsp/boards/bsp_delay.c"}, {"path": "bsp/boards/bsp_flash.c"}, {"path": "bsp/boards/bsp_fric.c"}, {"path": "bsp/boards/bsp_imu_pwm.c"}, {"path": "bsp/boards/bsp_i2c.c"}, {"path": "bsp/boards/bsp_laser.c"}, {"path": "bsp/boards/bsp_led.c"}, {"path": "bsp/boards/bsp_rc.c"}, {"path": "bsp/boards/bsp_rng.c"}, {"path": "bsp/boards/bsp_spi.c"}, {"path": "bsp/boards/bsp_usart.c"}, {"path": "bsp/boards/bsp_servo_pwm.c"}], "folders": []}, {"name": "applications", "files": [{"path": "application/calibrate_task.c"}, {"path": "application/CAN_receive.c"}, {"path": "application/chassis_behaviour.c"}, {"path": "application/chassis_task.c"}, {"path": "application/detect_task.c"}, {"path": "application/gimbal_behaviour.c"}, {"path": "application/gimbal_task.c"}, {"path": "application/INS_task.c"}, {"path": "application/referee.c"}, {"path": "application/remote_control.c"}, {"path": "application/test_task.c"}, {"path": "application/usb_task.c"}, {"path": "application/referee_usart_task.c"}, {"path": "application/led_flow_task.c"}, {"path": "application/UI/UI_task.c"}, {"path": "application/vision.c"}, {"path": "application/kalman_filter.c"}, {"path": "application/shoot_task.c"}, {"path": "application/music_task.c"}, {"path": "application/UI/RM_Cilent_UI.c"}, {"path": "application/voltage_task.c"}, {"path": "application/Vision_task.c"}], "folders": []}, {"name": "devices", "files": [{"path": "components/devices/OLED.c"}, {"path": "components/devices/BMI088driver.c"}, {"path": "components/devices/BMI088Middleware.c"}, {"path": "components/devices/ist8310driver.c"}, {"path": "components/devices/ist8310driver_middleware.c"}], "folders": []}, {"name": "algorithm", "files": [{"path": "components/algorithm/AHRS.lib"}, {"path": "components/algorithm/AHRS_middleware.c"}, {"path": "components/algorithm/arm_cortexM4lf_math.lib"}, {"path": "components/algorithm/user_lib.c"}], "folders": []}, {"name": "support", "files": [{"path": "components/support/CRC8_CRC16.c"}, {"path": "components/support/fifo.c"}, {"path": "components/support/mem_mang4.c"}, {"path": "components/support/filter.c"}, {"path": "components/support/mymath.c"}, {"path": "components/support/ramp.c"}], "folders": []}, {"name": "controller", "files": [{"path": "components/controller/pid.c"}, {"path": "components/controller/New_pid.c"}], "folders": []}, {"name": "Middlewares", "files": [], "folders": [{"name": "FreeRTOS", "files": [{"path": "Middlewares/Third_Party/FreeRTOS/Source/croutine.c"}, {"path": "Middlewares/Third_Party/FreeRTOS/Source/event_groups.c"}, {"path": "Middlewares/Third_Party/FreeRTOS/Source/list.c"}, {"path": "Middlewares/Third_Party/FreeRTOS/Source/queue.c"}, {"path": "Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c"}, {"path": "Middlewares/Third_Party/FreeRTOS/Source/tasks.c"}, {"path": "Middlewares/Third_Party/FreeRTOS/Source/timers.c"}, {"path": "Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS/cmsis_os.c"}, {"path": "Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c"}, {"path": "Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/port.c"}], "folders": []}, {"name": "USB_Device_Library", "files": [{"path": "Middlewares/ST/STM32_USB_Device_Library/Core/Src/usbd_core.c"}, {"path": "Middlewares/ST/STM32_USB_Device_Library/Core/Src/usbd_ctlreq.c"}, {"path": "Middlewares/ST/STM32_USB_Device_Library/Core/Src/usbd_ioreq.c"}, {"path": "Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Src/usbd_cdc.c"}], "folders": []}]}, {"name": "UI", "files": [{"path": "application/UI/ui_interface.c"}, {"path": "application/UI/codes/ui_g_Dynamic_0.c"}, {"path": "application/UI/codes/ui_g_Dynamic_1.c"}, {"path": "application/UI/codes/ui_g_Dynamic_2.c"}, {"path": "application/UI/codes/ui_g_Dynamic_3.c"}, {"path": "application/UI/codes/ui_g_Ungroup_0.c"}, {"path": "application/UI/codes/ui_g_Ungroup_1.c"}, {"path": "application/UI/codes/ui_g_Ungroup_2.c"}, {"path": "application/UI/codes/ui_g_Ungroup_3.c"}, {"path": "application/UI/codes/ui_g_Ungroup_4.c"}, {"path": "application/UI/codes/ui_g_Ungroup_5.c"}], "folders": []}]}, "outDir": "build", "deviceName": null, "packDir": null, "miscInfo": {"uid": "a5145324147b05ea62c155d645b4c967"}, "targets": {"VESTA": {"excludeList": [], "toolchain": "AC5", "compileConfig": {"cpuType": "Cortex-M4", "archExtensions": "", "floatingPointHardware": "single", "scatterFilePath": "MDK-ARM/VESTA.VESTA.sct", "useCustomScatterFile": false, "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x20000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x10000000", "size": "0x10000"}, "isChecked": true, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x8000000", "size": "0x100000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}]}, "options": "null"}, "uploader": "JLink", "uploadConfig": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}, "uploadConfigMap": {}, "custom_dep": {"name": "default", "incList": ["Inc", "Drivers/STM32F4xx_HAL_Driver/Inc", "Drivers/STM32F4xx_HAL_Driver/Inc/Legacy", "Middlewares/Third_Party/FreeRTOS/Source/include", "Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS", "Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F", "Drivers/CMSIS/Device/ST/STM32F4xx/Include", "Drivers/CMSIS/Include", "application", "bsp/boards", "components/devices", "components/algorithm", "components/algorithm/Include", "components/support", "application/protocol", "components/controller", "Middlewares/ST/STM32_USB_Device_Library/Core/Inc", "Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc", "application/UI", "application/UI/codes", ".cmsis/include", "MDK-ARM/RTE/_VESTA"], "libList": [], "defineList": ["USE_HAL_DRIVER", "STM32F407xx", "USE_HAL_DRIVER", "STM32F407xx", "ARM_MATH_CM4", "__FPU_USED=1U", "__FPU_PRESENT=1U", "__CC_ARM", "ARM_MATH_MATRIX_CHECK", "ARM_MATH_ROUNDING"]}, "builderOptions": {"AC5": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"use-microLIB": true, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-0", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "-O1 -g -W", "CXX_FLAGS": "-O1 -g -W", "warnings": "all-warnings"}, "asm-compiler": {}, "linker": {"$outputTaskExcludes": [".bin"], "output-format": "elf", "xo-base": "", "ro-base": "0x08000000", "rw-base": "0x20000000"}}}}}, "version": "3.6"}