[{"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_adc.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_adc.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_adc.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_adc.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_adc_ex.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_adc_ex.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_adc_ex.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_adc_ex.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_can.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_can.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_can.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_can.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_cortex.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_cortex.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_cortex.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_cortex.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_crc.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_crc.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_crc.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_crc.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma_ex.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma_ex.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma_ex.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma_ex.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_exti.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_exti.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_exti.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_exti.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ex.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ex.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ex.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ex.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ramfunc.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ramfunc.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ramfunc.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ramfunc.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_gpio.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_gpio.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_gpio.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_gpio.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c_ex.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c_ex.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c_ex.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c_ex.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pcd.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pcd.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pcd.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pcd.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pcd_ex.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pcd_ex.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pcd_ex.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pcd_ex.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr_ex.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr_ex.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr_ex.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr_ex.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc_ex.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc_ex.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc_ex.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc_ex.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rng.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rng.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rng.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rng.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rtc.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rtc.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rtc.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rtc.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rtc_ex.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rtc_ex.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rtc_ex.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rtc_ex.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_spi.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_spi.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_spi.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_spi.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim_ex.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim_ex.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim_ex.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim_ex.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_uart.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_uart.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_uart.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_uart.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_ll_usb.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_ll_usb.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_ll_usb.d .\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_ll_usb.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\MDK-ARM\\startup_stm32f407xx.s", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armasm.exe\" --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA --cpu Cortex-M4.fp --li --pd \"__MICROLIB SETA 1\" -g -o .\\build\\VESTA\\.obj\\MDK-ARM\\startup_stm32f407xx.o --depend .\\build\\VESTA\\.obj\\MDK-ARM\\startup_stm32f407xx.d .\\MDK-ARM\\startup_stm32f407xx.s"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Middlewares\\ST\\STM32_USB_Device_Library\\Class\\CDC\\Src\\usbd_cdc.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Middlewares\\ST\\STM32_USB_Device_Library\\Class\\CDC\\Src\\usbd_cdc.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Middlewares\\ST\\STM32_USB_Device_Library\\Class\\CDC\\Src\\usbd_cdc.d .\\Middlewares\\ST\\STM32_USB_Device_Library\\Class\\CDC\\Src\\usbd_cdc.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Middlewares\\ST\\STM32_USB_Device_Library\\Core\\Src\\usbd_core.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Middlewares\\ST\\STM32_USB_Device_Library\\Core\\Src\\usbd_core.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Middlewares\\ST\\STM32_USB_Device_Library\\Core\\Src\\usbd_core.d .\\Middlewares\\ST\\STM32_USB_Device_Library\\Core\\Src\\usbd_core.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Middlewares\\ST\\STM32_USB_Device_Library\\Core\\Src\\usbd_ctlreq.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Middlewares\\ST\\STM32_USB_Device_Library\\Core\\Src\\usbd_ctlreq.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Middlewares\\ST\\STM32_USB_Device_Library\\Core\\Src\\usbd_ctlreq.d .\\Middlewares\\ST\\STM32_USB_Device_Library\\Core\\Src\\usbd_ctlreq.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Middlewares\\ST\\STM32_USB_Device_Library\\Core\\Src\\usbd_ioreq.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Middlewares\\ST\\STM32_USB_Device_Library\\Core\\Src\\usbd_ioreq.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Middlewares\\ST\\STM32_USB_Device_Library\\Core\\Src\\usbd_ioreq.d .\\Middlewares\\ST\\STM32_USB_Device_Library\\Core\\Src\\usbd_ioreq.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Middlewares\\Third_Party\\FreeRTOS\\Source\\CMSIS_RTOS\\cmsis_os.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Middlewares\\Third_Party\\FreeRTOS\\Source\\CMSIS_RTOS\\cmsis_os.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Middlewares\\Third_Party\\FreeRTOS\\Source\\CMSIS_RTOS\\cmsis_os.d .\\Middlewares\\Third_Party\\FreeRTOS\\Source\\CMSIS_RTOS\\cmsis_os.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Middlewares\\Third_Party\\FreeRTOS\\Source\\croutine.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Middlewares\\Third_Party\\FreeRTOS\\Source\\croutine.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Middlewares\\Third_Party\\FreeRTOS\\Source\\croutine.d .\\Middlewares\\Third_Party\\FreeRTOS\\Source\\croutine.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Middlewares\\Third_Party\\FreeRTOS\\Source\\event_groups.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Middlewares\\Third_Party\\FreeRTOS\\Source\\event_groups.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Middlewares\\Third_Party\\FreeRTOS\\Source\\event_groups.d .\\Middlewares\\Third_Party\\FreeRTOS\\Source\\event_groups.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Middlewares\\Third_Party\\FreeRTOS\\Source\\list.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Middlewares\\Third_Party\\FreeRTOS\\Source\\list.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Middlewares\\Third_Party\\FreeRTOS\\Source\\list.d .\\Middlewares\\Third_Party\\FreeRTOS\\Source\\list.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\MemMang\\heap_4.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\MemMang\\heap_4.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\MemMang\\heap_4.d .\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\MemMang\\heap_4.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\RVDS\\ARM_CM4F\\port.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\RVDS\\ARM_CM4F\\port.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\RVDS\\ARM_CM4F\\port.d .\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\RVDS\\ARM_CM4F\\port.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Middlewares\\Third_Party\\FreeRTOS\\Source\\queue.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Middlewares\\Third_Party\\FreeRTOS\\Source\\queue.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Middlewares\\Third_Party\\FreeRTOS\\Source\\queue.d .\\Middlewares\\Third_Party\\FreeRTOS\\Source\\queue.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Middlewares\\Third_Party\\FreeRTOS\\Source\\stream_buffer.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Middlewares\\Third_Party\\FreeRTOS\\Source\\stream_buffer.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Middlewares\\Third_Party\\FreeRTOS\\Source\\stream_buffer.d .\\Middlewares\\Third_Party\\FreeRTOS\\Source\\stream_buffer.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Middlewares\\Third_Party\\FreeRTOS\\Source\\tasks.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Middlewares\\Third_Party\\FreeRTOS\\Source\\tasks.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Middlewares\\Third_Party\\FreeRTOS\\Source\\tasks.d .\\Middlewares\\Third_Party\\FreeRTOS\\Source\\tasks.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Middlewares\\Third_Party\\FreeRTOS\\Source\\timers.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Middlewares\\Third_Party\\FreeRTOS\\Source\\timers.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Middlewares\\Third_Party\\FreeRTOS\\Source\\timers.d .\\Middlewares\\Third_Party\\FreeRTOS\\Source\\timers.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Src\\adc.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Src\\adc.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Src\\adc.d .\\Src\\adc.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Src\\can.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Src\\can.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Src\\can.d .\\Src\\can.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Src\\crc.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Src\\crc.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Src\\crc.d .\\Src\\crc.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Src\\dma.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Src\\dma.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Src\\dma.d .\\Src\\dma.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Src\\freertos.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Src\\freertos.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Src\\freertos.d .\\Src\\freertos.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Src\\gpio.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Src\\gpio.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Src\\gpio.d .\\Src\\gpio.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Src\\i2c.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Src\\i2c.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Src\\i2c.d .\\Src\\i2c.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Src\\main.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Src\\main.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Src\\main.d .\\Src\\main.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Src\\pm01_api.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Src\\pm01_api.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Src\\pm01_api.d .\\Src\\pm01_api.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Src\\rng.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Src\\rng.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Src\\rng.d .\\Src\\rng.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Src\\rtc.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Src\\rtc.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Src\\rtc.d .\\Src\\rtc.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Src\\spi.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Src\\spi.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Src\\spi.d .\\Src\\spi.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Src\\stm32f4xx_hal_msp.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Src\\stm32f4xx_hal_msp.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Src\\stm32f4xx_hal_msp.d .\\Src\\stm32f4xx_hal_msp.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Src\\stm32f4xx_it.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Src\\stm32f4xx_it.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Src\\stm32f4xx_it.d .\\Src\\stm32f4xx_it.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Src\\system_stm32f4xx.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Src\\system_stm32f4xx.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Src\\system_stm32f4xx.d .\\Src\\system_stm32f4xx.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Src\\tim.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Src\\tim.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Src\\tim.d .\\Src\\tim.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Src\\usart.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Src\\usart.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Src\\usart.d .\\Src\\usart.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Src\\usb_device.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Src\\usb_device.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Src\\usb_device.d .\\Src\\usb_device.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Src\\usbd_cdc_if.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Src\\usbd_cdc_if.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Src\\usbd_cdc_if.d .\\Src\\usbd_cdc_if.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Src\\usbd_conf.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Src\\usbd_conf.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Src\\usbd_conf.d .\\Src\\usbd_conf.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\Src\\usbd_desc.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\Src\\usbd_desc.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\Src\\usbd_desc.d .\\Src\\usbd_desc.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\CAN_receive.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\CAN_receive.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\CAN_receive.d .\\application\\CAN_receive.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\INS_task.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\INS_task.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\INS_task.d .\\application\\INS_task.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\UI\\RM_Cilent_UI.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\UI\\RM_Cilent_UI.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\UI\\RM_Cilent_UI.d .\\application\\UI\\RM_Cilent_UI.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\UI\\UI_task.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\UI\\UI_task.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\UI\\UI_task.d .\\application\\UI\\UI_task.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\UI\\codes\\ui_g_Dynamic_0.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\UI\\codes\\ui_g_Dynamic_0.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\UI\\codes\\ui_g_Dynamic_0.d .\\application\\UI\\codes\\ui_g_Dynamic_0.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\UI\\codes\\ui_g_Dynamic_1.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\UI\\codes\\ui_g_Dynamic_1.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\UI\\codes\\ui_g_Dynamic_1.d .\\application\\UI\\codes\\ui_g_Dynamic_1.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\UI\\codes\\ui_g_Dynamic_2.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\UI\\codes\\ui_g_Dynamic_2.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\UI\\codes\\ui_g_Dynamic_2.d .\\application\\UI\\codes\\ui_g_Dynamic_2.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\UI\\codes\\ui_g_Dynamic_3.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\UI\\codes\\ui_g_Dynamic_3.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\UI\\codes\\ui_g_Dynamic_3.d .\\application\\UI\\codes\\ui_g_Dynamic_3.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\UI\\codes\\ui_g_Ungroup_0.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\UI\\codes\\ui_g_Ungroup_0.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\UI\\codes\\ui_g_Ungroup_0.d .\\application\\UI\\codes\\ui_g_Ungroup_0.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\UI\\codes\\ui_g_Ungroup_1.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\UI\\codes\\ui_g_Ungroup_1.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\UI\\codes\\ui_g_Ungroup_1.d .\\application\\UI\\codes\\ui_g_Ungroup_1.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\UI\\codes\\ui_g_Ungroup_2.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\UI\\codes\\ui_g_Ungroup_2.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\UI\\codes\\ui_g_Ungroup_2.d .\\application\\UI\\codes\\ui_g_Ungroup_2.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\UI\\codes\\ui_g_Ungroup_3.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\UI\\codes\\ui_g_Ungroup_3.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\UI\\codes\\ui_g_Ungroup_3.d .\\application\\UI\\codes\\ui_g_Ungroup_3.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\UI\\codes\\ui_g_Ungroup_4.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\UI\\codes\\ui_g_Ungroup_4.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\UI\\codes\\ui_g_Ungroup_4.d .\\application\\UI\\codes\\ui_g_Ungroup_4.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\UI\\codes\\ui_g_Ungroup_5.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\UI\\codes\\ui_g_Ungroup_5.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\UI\\codes\\ui_g_Ungroup_5.d .\\application\\UI\\codes\\ui_g_Ungroup_5.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\UI\\ui_interface.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\UI\\ui_interface.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\UI\\ui_interface.d .\\application\\UI\\ui_interface.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\Vision_task.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\Vision_task.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\Vision_task.d .\\application\\Vision_task.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\calibrate_task.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\calibrate_task.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\calibrate_task.d .\\application\\calibrate_task.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\chassis_behaviour.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\chassis_behaviour.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\chassis_behaviour.d .\\application\\chassis_behaviour.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\chassis_task.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\chassis_task.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\chassis_task.d .\\application\\chassis_task.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\detect_task.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\detect_task.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\detect_task.d .\\application\\detect_task.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\gimbal_behaviour.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\gimbal_behaviour.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\gimbal_behaviour.d .\\application\\gimbal_behaviour.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\gimbal_task.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\gimbal_task.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\gimbal_task.d .\\application\\gimbal_task.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\kalman.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\kalman.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\kalman.d .\\application\\kalman.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\kalman_filter.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\kalman_filter.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\kalman_filter.d .\\application\\kalman_filter.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\led_flow_task.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\led_flow_task.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\led_flow_task.d .\\application\\led_flow_task.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\music_task.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\music_task.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\music_task.d .\\application\\music_task.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\referee.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\referee.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\referee.d .\\application\\referee.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\referee_usart_task.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\referee_usart_task.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\referee_usart_task.d .\\application\\referee_usart_task.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\remote_control.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\remote_control.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\remote_control.d .\\application\\remote_control.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\shoot_task.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\shoot_task.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\shoot_task.d .\\application\\shoot_task.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\test_task.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\test_task.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\test_task.d .\\application\\test_task.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\usb_task.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\usb_task.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\usb_task.d .\\application\\usb_task.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\vision.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\vision.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\vision.d .\\application\\vision.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\application\\voltage_task.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\application\\voltage_task.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\application\\voltage_task.d .\\application\\voltage_task.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\bsp\\boards\\bsp_adc.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_adc.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_adc.d .\\bsp\\boards\\bsp_adc.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\bsp\\boards\\bsp_buzzer.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_buzzer.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_buzzer.d .\\bsp\\boards\\bsp_buzzer.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\bsp\\boards\\bsp_can.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_can.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_can.d .\\bsp\\boards\\bsp_can.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\bsp\\boards\\bsp_crc32.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_crc32.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_crc32.d .\\bsp\\boards\\bsp_crc32.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\bsp\\boards\\bsp_delay.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_delay.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_delay.d .\\bsp\\boards\\bsp_delay.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\bsp\\boards\\bsp_flash.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_flash.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_flash.d .\\bsp\\boards\\bsp_flash.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\bsp\\boards\\bsp_fric.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_fric.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_fric.d .\\bsp\\boards\\bsp_fric.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\bsp\\boards\\bsp_i2c.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_i2c.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_i2c.d .\\bsp\\boards\\bsp_i2c.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\bsp\\boards\\bsp_imu_pwm.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_imu_pwm.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_imu_pwm.d .\\bsp\\boards\\bsp_imu_pwm.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\bsp\\boards\\bsp_laser.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_laser.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_laser.d .\\bsp\\boards\\bsp_laser.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\bsp\\boards\\bsp_led.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_led.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_led.d .\\bsp\\boards\\bsp_led.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\bsp\\boards\\bsp_rc.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_rc.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_rc.d .\\bsp\\boards\\bsp_rc.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\bsp\\boards\\bsp_rng.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_rng.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_rng.d .\\bsp\\boards\\bsp_rng.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\bsp\\boards\\bsp_servo_pwm.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_servo_pwm.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_servo_pwm.d .\\bsp\\boards\\bsp_servo_pwm.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\bsp\\boards\\bsp_spi.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_spi.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_spi.d .\\bsp\\boards\\bsp_spi.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\bsp\\boards\\bsp_usart.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_usart.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\bsp\\boards\\bsp_usart.d .\\bsp\\boards\\bsp_usart.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\components\\algorithm\\AHRS_middleware.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\components\\algorithm\\AHRS_middleware.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\components\\algorithm\\AHRS_middleware.d .\\components\\algorithm\\AHRS_middleware.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\components\\algorithm\\user_lib.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\components\\algorithm\\user_lib.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\components\\algorithm\\user_lib.d .\\components\\algorithm\\user_lib.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\components\\controller\\New_pid.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\components\\controller\\New_pid.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\components\\controller\\New_pid.d .\\components\\controller\\New_pid.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\components\\controller\\pid.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\components\\controller\\pid.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\components\\controller\\pid.d .\\components\\controller\\pid.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\components\\devices\\BMI088Middleware.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\components\\devices\\BMI088Middleware.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\components\\devices\\BMI088Middleware.d .\\components\\devices\\BMI088Middleware.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\components\\devices\\BMI088driver.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\components\\devices\\BMI088driver.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\components\\devices\\BMI088driver.d .\\components\\devices\\BMI088driver.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\components\\devices\\OLED.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\components\\devices\\OLED.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\components\\devices\\OLED.d .\\components\\devices\\OLED.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\components\\devices\\ist8310driver.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\components\\devices\\ist8310driver.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\components\\devices\\ist8310driver.d .\\components\\devices\\ist8310driver.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\components\\devices\\ist8310driver_middleware.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\components\\devices\\ist8310driver_middleware.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\components\\devices\\ist8310driver_middleware.d .\\components\\devices\\ist8310driver_middleware.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\components\\support\\CRC8_CRC16.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\components\\support\\CRC8_CRC16.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\components\\support\\CRC8_CRC16.d .\\components\\support\\CRC8_CRC16.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\components\\support\\fifo.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\components\\support\\fifo.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\components\\support\\fifo.d .\\components\\support\\fifo.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\components\\support\\filter.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\components\\support\\filter.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\components\\support\\filter.d .\\components\\support\\filter.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\components\\support\\mem_mang4.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\components\\support\\mem_mang4.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\components\\support\\mem_mang4.d .\\components\\support\\mem_mang4.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\components\\support\\mymath.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\components\\support\\mymath.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\components\\support\\mymath.d .\\components\\support\\mymath.c"}, {"directory": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "file": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\components\\support\\ramp.c", "command": "\"C:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -IInc -IDrivers/STM32F4xx_HAL_Driver/Inc -IDrivers/STM32F4xx_HAL_Driver/Inc/Legacy -IMiddlewares/Third_Party/FreeRTOS/Source/include -IMiddlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -IMiddlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -IDrivers/CMSIS/Device/ST/STM32F4xx/Include -IDrivers/CMSIS/Include -Iapplication -Ibsp/boards -Icomponents/devices -Icomponents/algorithm -Icomponents/algorithm/Include -Icomponents/support -Iapplication/protocol -Icomponents/controller -IMiddlewares/ST/STM32_USB_Device_Library/Core/Inc -IMiddlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -Iapplication/UI -Iapplication/UI/codes -I.cmsis/include -IMDK-ARM/RTE/_VESTA -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" -D\"ARM_MATH_CM4\" -D\"__FPU_USED=1U\" -D\"__FPU_PRESENT=1U\" -D\"__CC_ARM\" -D\"ARM_MATH_MATRIX_CHECK\" -D\"ARM_MATH_ROUNDING\" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections -O1 -g -W -g -o .\\build\\VESTA\\.obj\\components\\support\\ramp.o --no_depend_system_headers --depend .\\build\\VESTA\\.obj\\components\\support\\ramp.d .\\components\\support\\ramp.c"}]