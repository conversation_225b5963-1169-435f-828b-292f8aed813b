.\vesta\ui_interface.o: ..\application\UI\ui_interface.c
.\vesta\ui_interface.o: ..\application\UI\ui_interface.h
.\vesta\ui_interface.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\vesta\ui_interface.o: ..\application\UI\ui_types.h
.\vesta\ui_interface.o: ..\application\struct_typedef.h
.\vesta\ui_interface.o: ../Inc/usart.h
.\vesta\ui_interface.o: ../Inc/main.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal.h
.\vesta\ui_interface.o: ../Inc/stm32f4xx_hal_conf.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_rcc.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_def.h
.\vesta\ui_interface.o: ../Drivers/CMSIS/Device/ST/STM32F4xx/Include/stm32f4xx.h
.\vesta\ui_interface.o: ../Drivers/CMSIS/Device/ST/STM32F4xx/Include/stm32f407xx.h
.\vesta\ui_interface.o: ../Drivers/CMSIS/Include/core_cm4.h
.\vesta\ui_interface.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\vesta\ui_interface.o: ../Drivers/CMSIS/Include/cmsis_version.h
.\vesta\ui_interface.o: ../Drivers/CMSIS/Include/cmsis_compiler.h
.\vesta\ui_interface.o: ../Drivers/CMSIS/Include/cmsis_armcc.h
.\vesta\ui_interface.o: ../Drivers/CMSIS/Include/mpu_armv7.h
.\vesta\ui_interface.o: ../Drivers/CMSIS/Device/ST/STM32F4xx/Include/system_stm32f4xx.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h
.\vesta\ui_interface.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stddef.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_rcc_ex.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_exti.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_gpio.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_gpio_ex.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_dma.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_dma_ex.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_cortex.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_adc.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_adc_ex.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_can.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_crc.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_flash.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_flash_ex.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_flash_ramfunc.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_i2c.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_i2c_ex.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_pwr.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_pwr_ex.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_rng.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_rtc.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_rtc_ex.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_spi.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_tim.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_tim_ex.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_uart.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_pcd.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_ll_usb.h
.\vesta\ui_interface.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_pcd_ex.h
.\vesta\ui_interface.o: ..\bsp\boards\bsp_usart.h
.\vesta\ui_interface.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\vesta\ui_interface.o: ..\components\support\CRC8_CRC16.h
.\vesta\ui_interface.o: ..\application\UI\RM_Cilent_UI.h
.\vesta\ui_interface.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdarg.h
