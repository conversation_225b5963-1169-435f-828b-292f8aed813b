{"name": "VESTA", "target": "VESTA", "toolchain": "AC5", "toolchainLocation": "C:\\Keil_v5\\ARM\\ARMCC", "toolchainCfgFile": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.23.12\\res\\data\\models/arm.v5.model.json", "buildMode": "fast|multhread", "showRepathOnLog": true, "threadNum": 32, "rootDir": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "dumpPath": "build\\VESTA", "outDir": "build\\VESTA", "ram": 196608, "rom": 1048576, "incDirs": ["Inc", "Drivers/STM32F4xx_HAL_Driver/Inc", "Drivers/STM32F4xx_HAL_Driver/Inc/Legacy", "Middlewares/Third_Party/FreeRTOS/Source/include", "Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS", "Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F", "Drivers/CMSIS/Device/ST/STM32F4xx/Include", "Drivers/CMSIS/Include", "application", "bsp/boards", "components/devices", "components/algorithm", "components/algorithm/Include", "components/support", "application/protocol", "components/controller", "Middlewares/ST/STM32_USB_Device_Library/Core/Inc", "Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc", "application/UI", "application/UI/codes", ".cmsis/include", "MDK-ARM/RTE/_VESTA"], "libDirs": [], "defines": ["USE_HAL_DRIVER", "STM32F407xx", "ARM_MATH_CM4", "__FPU_USED=1U", "__FPU_PRESENT=1U", "__CC_ARM", "ARM_MATH_MATRIX_CHECK", "ARM_MATH_ROUNDING"], "sourceList": ["Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_can.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_crc.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pcd.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pcd_ex.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rng.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rtc.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rtc_ex.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c", "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_usb.c", "MDK-ARM/startup_stm32f407xx.s", "Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Src/usbd_cdc.c", "Middlewares/ST/STM32_USB_Device_Library/Core/Src/usbd_core.c", "Middlewares/ST/STM32_USB_Device_Library/Core/Src/usbd_ctlreq.c", "Middlewares/ST/STM32_USB_Device_Library/Core/Src/usbd_ioreq.c", "Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS/cmsis_os.c", "Middlewares/Third_Party/FreeRTOS/Source/croutine.c", "Middlewares/Third_Party/FreeRTOS/Source/event_groups.c", "Middlewares/Third_Party/FreeRTOS/Source/list.c", "Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c", "Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/port.c", "Middlewares/Third_Party/FreeRTOS/Source/queue.c", "Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c", "Middlewares/Third_Party/FreeRTOS/Source/tasks.c", "Middlewares/Third_Party/FreeRTOS/Source/timers.c", "Src/adc.c", "Src/can.c", "Src/crc.c", "Src/dma.c", "Src/freertos.c", "Src/gpio.c", "Src/i2c.c", "Src/main.c", "Src/pm01_api.c", "Src/rng.c", "Src/rtc.c", "Src/spi.c", "Src/stm32f4xx_hal_msp.c", "Src/stm32f4xx_it.c", "Src/system_stm32f4xx.c", "Src/tim.c", "Src/usart.c", "Src/usb_device.c", "Src/usbd_cdc_if.c", "Src/usbd_conf.c", "Src/usbd_desc.c", "application/CAN_receive.c", "application/INS_task.c", "application/UI/RM_Cilent_UI.c", "application/UI/UI_task.c", "application/UI/codes/ui_g_Dynamic_0.c", "application/UI/codes/ui_g_Dynamic_1.c", "application/UI/codes/ui_g_Dynamic_2.c", "application/UI/codes/ui_g_Dynamic_3.c", "application/UI/codes/ui_g_Ungroup_0.c", "application/UI/codes/ui_g_Ungroup_1.c", "application/UI/codes/ui_g_Ungroup_2.c", "application/UI/codes/ui_g_Ungroup_3.c", "application/UI/codes/ui_g_Ungroup_4.c", "application/UI/codes/ui_g_Ungroup_5.c", "application/UI/ui_interface.c", "application/Vision_task.c", "application/calibrate_task.c", "application/chassis_behaviour.c", "application/chassis_task.c", "application/detect_task.c", "application/gimbal_behaviour.c", "application/gimbal_task.c", "application/kalman.c", "application/kalman_filter.c", "application/led_flow_task.c", "application/music_task.c", "application/referee.c", "application/referee_usart_task.c", "application/remote_control.c", "application/shoot_task.c", "application/test_task.c", "application/usb_task.c", "application/vision.c", "application/voltage_task.c", "bsp/boards/bsp_adc.c", "bsp/boards/bsp_buzzer.c", "bsp/boards/bsp_can.c", "bsp/boards/bsp_crc32.c", "bsp/boards/bsp_delay.c", "bsp/boards/bsp_flash.c", "bsp/boards/bsp_fric.c", "bsp/boards/bsp_i2c.c", "bsp/boards/bsp_imu_pwm.c", "bsp/boards/bsp_laser.c", "bsp/boards/bsp_led.c", "bsp/boards/bsp_rc.c", "bsp/boards/bsp_rng.c", "bsp/boards/bsp_servo_pwm.c", "bsp/boards/bsp_spi.c", "bsp/boards/bsp_usart.c", "components/algorithm/AHRS.lib", "components/algorithm/AHRS_middleware.c", "components/algorithm/arm_cortexM4lf_math.lib", "components/algorithm/user_lib.c", "components/controller/New_pid.c", "components/controller/pid.c", "components/devices/BMI088Middleware.c", "components/devices/BMI088driver.c", "components/devices/OLED.c", "components/devices/ist8310driver.c", "components/devices/ist8310driver_middleware.c", "components/support/CRC8_CRC16.c", "components/support/fifo.c", "components/support/filter.c", "components/support/mem_mang4.c", "components/support/mymath.c", "components/support/ramp.c"], "alwaysInBuildSources": [], "sourceParams": {}, "options": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [{"name": "axf to elf", "command": "axf2elf -d \"${ToolchainRoot}\" -i \"${OutDir}/${ProjectName}.axf\" -o \"${OutDir}/${ProjectName}.elf\" > \"${OutDir}/axf2elf.log\""}], "global": {"use-microLIB": true, "output-debug-info": "enable", "microcontroller-cpu": "cortex-m4-sp", "microcontroller-fpu": "cortex-m4-sp", "microcontroller-float": "cortex-m4-sp", "$arch-extensions": "", "$clang-arch-extensions": "", "$armlink-arch-extensions": ""}, "c/cpp-compiler": {"optimization": "level-0", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "-O1 -g -W", "CXX_FLAGS": "-O1 -g -W", "warnings": "all-warnings"}, "asm-compiler": {}, "linker": {"$outputTaskExcludes": [".bin"], "output-format": "elf", "xo-base": "", "ro-base": "0x08000000", "rw-base": "0x20000000", "link-scatter": ["c:/Users/<USER>/code/Refator/omi-season/build/VESTA/VESTA.sct"]}}, "env": {"KEIL_OUTPUT_DIR": "VESTA", "workspaceFolder": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "workspaceFolderBasename": "omi-season", "OutDir": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\build\\VESTA", "OutDirRoot": "build", "OutDirBase": "build\\VESTA", "ProjectName": "VESTA", "ConfigName": "VESTA", "ProjectRoot": "c:\\Users\\<USER>\\code\\Refator\\omi-season", "ExecutableName": "c:\\Users\\<USER>\\code\\Refator\\omi-season\\build\\VESTA\\VESTA", "ChipPackDir": "", "ChipName": "", "SYS_Platform": "win32", "SYS_DirSep": "\\", "SYS_DirSeparator": "\\", "SYS_PathSep": ";", "SYS_PathSeparator": ";", "SYS_EOL": "\r\n", "EIDE_BUILDER_DIR": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.23.12\\res\\tools\\win32\\unify_builder", "EIDE_BINARIES_VER": "12.1.1", "EIDE_MSYS": "C:\\Users\\<USER>\\.eide\\bin\\builder\\msys\\bin", "EIDE_PY3_CMD": "C:\\Users\\<USER>\\.eide\\bin\\python36\\python3.exe", "ToolchainRoot": "C:\\Keil_v5\\ARM\\ARMCC"}, "sysPaths": []}