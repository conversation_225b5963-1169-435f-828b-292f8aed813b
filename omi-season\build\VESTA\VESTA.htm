<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\build\VESTA\VESTA.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\build\VESTA\VESTA.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Fri Aug 01 20:20:36 2025
<BR><P>
<H3>Maximum Stack Usage =        504 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; MX_FREERTOS_Init &rArr; osThreadCreate &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[39]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[39]">ADC_IRQHandler</a><BR>
 <LI><a href="#[21]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[21]">BusFault_Handler</a><BR>
 <LI><a href="#[1f]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1f]">HardFault_Handler</a><BR>
 <LI><a href="#[20]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[20]">MemManage_Handler</a><BR>
 <LI><a href="#[22]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[22]">UsageFault_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[39]">ADC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[21]">BusFault_Handler</a> from stm32f4xx_it.o(i.BusFault_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3b]">CAN1_RX0_IRQHandler</a> from stm32f4xx_it.o(i.CAN1_RX0_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3c]">CAN1_RX1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3d]">CAN1_SCE_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3a]">CAN1_TX_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[67]">CAN2_RX0_IRQHandler</a> from stm32f4xx_it.o(i.CAN2_RX0_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[68]">CAN2_RX1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[69]">CAN2_SCE_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[66]">CAN2_TX_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[d]">CDC_Control_FS</a> from usbd_cdc_if.o(i.CDC_Control_FS) referenced 2 times from usbd_cdc_if.o(.data)
 <LI><a href="#[c]">CDC_DeInit_FS</a> from usbd_cdc_if.o(i.CDC_DeInit_FS) referenced 2 times from usbd_cdc_if.o(.data)
 <LI><a href="#[b]">CDC_Init_FS</a> from usbd_cdc_if.o(i.CDC_Init_FS) referenced 2 times from usbd_cdc_if.o(.data)
 <LI><a href="#[e]">CDC_Receive_FS</a> from usbd_cdc_if.o(i.CDC_Receive_FS) referenced 2 times from usbd_cdc_if.o(.data)
 <LI><a href="#[75]">DCMI_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[32]">DMA1_Stream0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[33]">DMA1_Stream1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[34]">DMA1_Stream2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[35]">DMA1_Stream3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[36]">DMA1_Stream4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[37]">DMA1_Stream5_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[38]">DMA1_Stream6_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[56]">DMA1_Stream7_IRQHandler</a> from bsp_i2c.o(i.DMA1_Stream7_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5f]">DMA2_Stream0_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[60]">DMA2_Stream1_IRQHandler</a> from stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[61]">DMA2_Stream2_IRQHandler</a> from INS_task.o(i.DMA2_Stream2_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[62]">DMA2_Stream3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[63]">DMA2_Stream4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[6b]">DMA2_Stream5_IRQHandler</a> from stm32f4xx_it.o(i.DMA2_Stream5_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[6c]">DMA2_Stream6_IRQHandler</a> from stm32f4xx_it.o(i.DMA2_Stream6_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[6d]">DMA2_Stream7_IRQHandler</a> from stm32f4xx_it.o(i.DMA2_Stream7_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[24]">DebugMon_Handler</a> from stm32f4xx_it.o(i.DebugMon_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[64]">ETH_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[65]">ETH_WKUP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2d]">EXTI0_IRQHandler</a> from stm32f4xx_it.o(i.EXTI0_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4f]">EXTI15_10_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2e]">EXTI1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2f]">EXTI2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[30]">EXTI3_IRQHandler</a> from stm32f4xx_it.o(i.EXTI3_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[31]">EXTI4_IRQHandler</a> from stm32f4xx_it.o(i.EXTI4_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3e]">EXTI9_5_IRQHandler</a> from stm32f4xx_it.o(i.EXTI9_5_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2b]">FLASH_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[57]">FMC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[77]">FPU_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[76]">HASH_RNG_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1f]">HardFault_Handler</a> from stm32f4xx_it.o(i.HardFault_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[47]">I2C1_ER_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[46]">I2C1_EV_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[49]">I2C2_ER_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[48]">I2C2_EV_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[70]">I2C3_ER_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[6f]">I2C3_EV_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[84]">INS_task</a> from INS_task.o(i.INS_task) referenced from freertos.o(.constdata)
 <LI><a href="#[20]">MemManage_Handler</a> from stm32f4xx_it.o(i.MemManage_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1e]">NMI_Handler</a> from stm32f4xx_it.o(i.NMI_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[6a]">OTG_FS_IRQHandler</a> from stm32f4xx_it.o(i.OTG_FS_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[51]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[72]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[71]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[74]">OTG_HS_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[73]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[28]">PVD_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[25]">PendSV_Handler</a> from port.o(.emb_text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2c]">RCC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[50]">RTC_Alarm_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[2a]">RTC_WKUP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[1d]">Reset_Handler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[58]">SDIO_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4a]">SPI1_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4b]">SPI2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5a]">SPI3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[23]">SVC_Handler</a> from port.o(.emb_text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[26]">SysTick_Handler</a> from stm32f4xx_it.o(i.SysTick_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[79]">SystemInit</a> from system_stm32f4xx.o(i.SystemInit) referenced from startup_stm32f407xx.o(.text)
 <LI><a href="#[29]">TAMP_STAMP_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[3f]">TIM1_BRK_TIM9_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[42]">TIM1_CC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[41]">TIM1_TRG_COM_TIM11_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[40]">TIM1_UP_TIM10_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[43]">TIM2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[44]">TIM3_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[45]">TIM4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[59]">TIM5_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5d]">TIM6_DAC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5e]">TIM7_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[52]">TIM8_BRK_TIM12_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[55]">TIM8_CC_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[54]">TIM8_TRG_COM_TIM14_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[53]">TIM8_UP_TIM13_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5b]">UART4_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5c]">UART5_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[7b]">UART_DMAAbortOnError</a> from stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) referenced from stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
 <LI><a href="#[4c]">USART1_IRQHandler</a> from stm32f4xx_it.o(i.USART1_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4d]">USART2_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[4e]">USART3_IRQHandler</a> from remote_control.o(i.USART3_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[6e]">USART6_IRQHandler</a> from referee_usart_task.o(i.USART6_IRQHandler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[5]">USBD_CDC_DataIn</a> from usbd_cdc.o(i.USBD_CDC_DataIn) referenced 2 times from usbd_cdc.o(.data)
 <LI><a href="#[6]">USBD_CDC_DataOut</a> from usbd_cdc.o(i.USBD_CDC_DataOut) referenced 2 times from usbd_cdc.o(.data)
 <LI><a href="#[2]">USBD_CDC_DeInit</a> from usbd_cdc.o(i.USBD_CDC_DeInit) referenced 2 times from usbd_cdc.o(.data)
 <LI><a href="#[4]">USBD_CDC_EP0_RxReady</a> from usbd_cdc.o(i.USBD_CDC_EP0_RxReady) referenced 2 times from usbd_cdc.o(.data)
 <LI><a href="#[a]">USBD_CDC_GetDeviceQualifierDescriptor</a> from usbd_cdc.o(i.USBD_CDC_GetDeviceQualifierDescriptor) referenced 2 times from usbd_cdc.o(.data)
 <LI><a href="#[8]">USBD_CDC_GetFSCfgDesc</a> from usbd_cdc.o(i.USBD_CDC_GetFSCfgDesc) referenced 2 times from usbd_cdc.o(.data)
 <LI><a href="#[7]">USBD_CDC_GetHSCfgDesc</a> from usbd_cdc.o(i.USBD_CDC_GetHSCfgDesc) referenced 2 times from usbd_cdc.o(.data)
 <LI><a href="#[9]">USBD_CDC_GetOtherSpeedCfgDesc</a> from usbd_cdc.o(i.USBD_CDC_GetOtherSpeedCfgDesc) referenced 2 times from usbd_cdc.o(.data)
 <LI><a href="#[1]">USBD_CDC_Init</a> from usbd_cdc.o(i.USBD_CDC_Init) referenced 2 times from usbd_cdc.o(.data)
 <LI><a href="#[3]">USBD_CDC_Setup</a> from usbd_cdc.o(i.USBD_CDC_Setup) referenced 2 times from usbd_cdc.o(.data)
 <LI><a href="#[14]">USBD_FS_ConfigStrDescriptor</a> from usbd_desc.o(i.USBD_FS_ConfigStrDescriptor) referenced 2 times from usbd_desc.o(.data)
 <LI><a href="#[f]">USBD_FS_DeviceDescriptor</a> from usbd_desc.o(i.USBD_FS_DeviceDescriptor) referenced 2 times from usbd_desc.o(.data)
 <LI><a href="#[15]">USBD_FS_InterfaceStrDescriptor</a> from usbd_desc.o(i.USBD_FS_InterfaceStrDescriptor) referenced 2 times from usbd_desc.o(.data)
 <LI><a href="#[10]">USBD_FS_LangIDStrDescriptor</a> from usbd_desc.o(i.USBD_FS_LangIDStrDescriptor) referenced 2 times from usbd_desc.o(.data)
 <LI><a href="#[11]">USBD_FS_ManufacturerStrDescriptor</a> from usbd_desc.o(i.USBD_FS_ManufacturerStrDescriptor) referenced 2 times from usbd_desc.o(.data)
 <LI><a href="#[12]">USBD_FS_ProductStrDescriptor</a> from usbd_desc.o(i.USBD_FS_ProductStrDescriptor) referenced 2 times from usbd_desc.o(.data)
 <LI><a href="#[13]">USBD_FS_SerialStrDescriptor</a> from usbd_desc.o(i.USBD_FS_SerialStrDescriptor) referenced 2 times from usbd_desc.o(.data)
 <LI><a href="#[22]">UsageFault_Handler</a> from stm32f4xx_it.o(i.UsageFault_Handler) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[27]">WWDG_IRQHandler</a> from startup_stm32f407xx.o(.text) referenced from startup_stm32f407xx.o(RESET)
 <LI><a href="#[7a]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_stm32f407xx.o(.text)
 <LI><a href="#[18]">_ui_update_g_Dynamic_0</a> from ui_g_Dynamic_0.o(i._ui_update_g_Dynamic_0) referenced 2 times from UI_task.o(.data)
 <LI><a href="#[17]">_ui_update_g_Dynamic_1</a> from ui_g_Dynamic_1.o(i._ui_update_g_Dynamic_1) referenced 2 times from UI_task.o(.data)
 <LI><a href="#[16]">_ui_update_g_Dynamic_2</a> from ui_g_Dynamic_2.o(i._ui_update_g_Dynamic_2) referenced 2 times from UI_task.o(.data)
 <LI><a href="#[19]">_ui_update_g_Dynamic_3</a> from ui_g_Dynamic_3.o(i._ui_update_g_Dynamic_3) referenced 2 times from UI_task.o(.data)
 <LI><a href="#[1b]">cali_gimbal_hook</a> from calibrate_task.o(i.cali_gimbal_hook) referenced 2 times from calibrate_task.o(.data)
 <LI><a href="#[1c]">cali_gyro_hook</a> from calibrate_task.o(i.cali_gyro_hook) referenced 2 times from calibrate_task.o(.data)
 <LI><a href="#[1a]">cali_head_hook</a> from calibrate_task.o(i.cali_head_hook) referenced 2 times from calibrate_task.o(.data)
 <LI><a href="#[80]">calibrate_task</a> from calibrate_task.o(i.calibrate_task) referenced from freertos.o(.constdata)
 <LI><a href="#[81]">chassis_task</a> from chassis_task.o(i.chassis_task) referenced from freertos.o(.constdata)
 <LI><a href="#[82]">detect_task</a> from detect_task.o(i.detect_task) referenced from freertos.o(.constdata)
 <LI><a href="#[83]">gimbal_task</a> from gimbal_task.o(i.gimbal_task) referenced from freertos.o(.constdata)
 <LI><a href="#[86]">led_RGB_flow_task</a> from led_flow_task.o(i.led_RGB_flow_task) referenced from freertos.o(.constdata)
 <LI><a href="#[78]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
 <LI><a href="#[88]">music_task</a> from music_task.o(i.music_task) referenced from freertos.o(.constdata)
 <LI><a href="#[7d]">prvIdleTask</a> from tasks.o(i.prvIdleTask) referenced from tasks.o(i.vTaskStartScheduler)
 <LI><a href="#[7c]">prvTaskExitError</a> from port.o(i.prvTaskExitError) referenced from port.o(i.pxPortInitialiseStack)
 <LI><a href="#[7e]">prvTimerTask</a> from timers.o(i.prvTimerTask) referenced from timers.o(i.xTimerCreateTimerTask)
 <LI><a href="#[89]">referee_usart_task</a> from referee_usart_task.o(i.referee_usart_task) referenced from freertos.o(.constdata)
 <LI><a href="#[85]">shoot_task</a> from shoot_task.o(i.shoot_task) referenced from freertos.o(.constdata)
 <LI><a href="#[7f]">test_task</a> from test_task.o(i.test_task) referenced from freertos.o(.constdata)
 <LI><a href="#[87]">ui_task</a> from UI_task.o(i.ui_task) referenced from freertos.o(.constdata)
 <LI><a href="#[8a]">usb_task</a> from usb_task.o(i.usb_task) referenced from freertos.o(.constdata)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[7a]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(.text)
</UL>
<P><STRONG><a name="[328]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[8b]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[a3]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[329]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[32a]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[32b]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[32c]"></a>__rt_lib_shutdown_fini</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry12b.o(.ARM.Collect$$$$0000000E))

<P><STRONG><a name="[32d]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[32e]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$00000011))

<P><STRONG><a name="[23]"></a>SVC_Handler</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[31d]"></a>__asm___6_port_c_39a90d8d__prvStartFirstTask</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[318]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
</UL>

<P><STRONG><a name="[31c]"></a>__asm___6_port_c_39a90d8d__prvEnableVFP</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[318]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
</UL>

<P><STRONG><a name="[25]"></a>PendSV_Handler</STRONG> (Thumb, 88 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR><BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSwitchContext
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[312]"></a>vPortGetIPSR</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[311]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortValidateInterruptPriority
</UL>

<P><STRONG><a name="[1d]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[68]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[69]"></a>CAN2_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[66]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[75]"></a>DCMI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[77]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[76]"></a>HASH_RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[70]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[6f]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[72]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[71]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[74]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[73]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>TIM1_BRK_TIM9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>TIM1_TRG_COM_TIM11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>TIM1_UP_TIM10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f407xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[a9]"></a>arm_cos_f32</STRONG> (Thumb, 108 bytes, Stack size 0 bytes, arm_cos_f32.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_feedback_update
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_key_Contorl
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_Contorl
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_cosf
</UL>

<P><STRONG><a name="[b1]"></a>arm_sin_f32</STRONG> (Thumb, 104 bytes, Stack size 0 bytes, arm_sin_f32.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chassis_follow_gimbal_yaw_control
<LI><a href="#[2ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_feedback_update
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_key_Contorl
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_Contorl
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_sinf
</UL>

<P><STRONG><a name="[8e]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
</UL>

<P><STRONG><a name="[28b]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataToQueue
<LI><a href="#[2dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataFromQueue
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_read_data
<LI><a href="#[289]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_read
<LI><a href="#[28a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cali_data_write
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fifo_s_puts
<LI><a href="#[2fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;referee_data_solve
</UL>

<P><STRONG><a name="[193]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Init
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FREERTOS_Init
<LI><a href="#[29b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;detect_init
</UL>

<P><STRONG><a name="[32f]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[92]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memset
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[330]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[331]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[91]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[2be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_referee_struct_data
</UL>

<P><STRONG><a name="[b4]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_MspInit
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_MspPostInit
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM8_Init
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM5_Init
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM10_Init
<LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_MspInit
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_update
</UL>

<P><STRONG><a name="[332]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[93]"></a>memset</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[30a]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetHandle
<LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_proc_string_frame
</UL>

<P><STRONG><a name="[26f]"></a>strcpy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, strcpy.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[274]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Ungroup_4
<LI><a href="#[273]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Ungroup_3
<LI><a href="#[272]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Ungroup_2
<LI><a href="#[271]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Ungroup_1
<LI><a href="#[26e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Ungroup_0
</UL>

<P><STRONG><a name="[94]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;girt_powerLimit
<LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_cali_gimbal_hook
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_key_Contorl
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_Contorl
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Omni_Move_Calculate
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Keyboard_Move_Calculate
<LI><a href="#[264]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[261]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[260]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[25f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
</UL>

<P><STRONG><a name="[98]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_update_g_Dynamic_0
<LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;girt_powerLimit
<LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_cali_gimbal_hook
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_Contorl
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Keyboard_Move_Calculate
<LI><a href="#[261]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[260]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[262]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>

<P><STRONG><a name="[99]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_update_g_Dynamic_0
<LI><a href="#[261]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[260]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[262]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>

<P><STRONG><a name="[9a]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_update_g_Dynamic_0
<LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;girt_powerLimit
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vision_Error_Angle_YAW
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vision_Error_Angle_PITCH
<LI><a href="#[2ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_feedback_update
<LI><a href="#[2ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;square
<LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;power_data_sent
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_Contorl
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Motor_Speed_PID
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Keyboard_Move_Calculate
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Key_MoveRamp
<LI><a href="#[264]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[261]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[260]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[266]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[262]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>

<P><STRONG><a name="[9b]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_RxFifo0MsgPendingCallback
<LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_update_g_Dynamic_0
<LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;girt_powerLimit
<LI><a href="#[2ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_feedback_update
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Motor_Speed_PID
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Keyboard_Move_Calculate
<LI><a href="#[25e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
</UL>

<P><STRONG><a name="[9c]"></a>__aeabi_i2d</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, dflti.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_i2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Motor_Speed_PID
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Keyboard_Move_Calculate
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Key_MoveRamp
<LI><a href="#[260]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
</UL>

<P><STRONG><a name="[9d]"></a>__aeabi_ui2d</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, dfltui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_ui2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_RxFifo0MsgPendingCallback
<LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;girt_powerLimit
<LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;power_data_sent
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Keyboard_Move_Calculate
<LI><a href="#[260]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
</UL>

<P><STRONG><a name="[9e]"></a>__aeabi_d2iz</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, dfixi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_d2iz
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Keyboard_Move_Calculate
<LI><a href="#[260]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[262]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>

<P><STRONG><a name="[9f]"></a>__aeabi_d2uiz</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, dfixui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2uiz
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_update_g_Dynamic_0
<LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;power_data_sent
</UL>

<P><STRONG><a name="[b8]"></a>__aeabi_f2d</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_update_g_Dynamic_0
<LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;girt_powerLimit
<LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_cali_gimbal_hook
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vision_Error_Angle_YAW
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vision_Error_Angle_PITCH
<LI><a href="#[2ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_feedback_update
<LI><a href="#[2a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_init_control
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_behavour_set
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_key_Contorl
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_Contorl
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Omni_Move_Calculate
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Motor_Speed_PID
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Keyboard_Move_Calculate
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Angle_error
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOL_SpeedStuck
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOL_PositStuck
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;activation_function
</UL>

<P><STRONG><a name="[333]"></a>__aeabi_cdcmpeq</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, cdcmple.o(.text), UNUSED)

<P><STRONG><a name="[f1]"></a>__aeabi_cdcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;girt_powerLimit
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_behavour_set
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_key_Contorl
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_Contorl
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Motor_Speed_PID
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Keyboard_Move_Calculate
</UL>

<P><STRONG><a name="[f0]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;girt_powerLimit
<LI><a href="#[2a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_init_control
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Keyboard_Move_Calculate
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOL_SpeedStuck
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOL_PositStuck
</UL>

<P><STRONG><a name="[a0]"></a>__aeabi_d2f</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, d2f.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_RxFifo0MsgPendingCallback
<LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;girt_powerLimit
<LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_cali_gimbal_hook
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vision_Error_Angle_YAW
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vision_Error_Angle_PITCH
<LI><a href="#[2ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_feedback_update
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_key_Contorl
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_Contorl
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Omni_Move_Calculate
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Motor_Speed_PID
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Keyboard_Move_Calculate
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Key_MoveRamp
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Angle_error
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;activation_function
</UL>

<P><STRONG><a name="[90]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[334]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[8f]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[335]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[95]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[336]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[337]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[a1]"></a>_float_round</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>

<P><STRONG><a name="[338]"></a>_float_epilogue</STRONG> (Thumb, 92 bytes, Stack size 4 bytes, fepilogue.o(.text), UNUSED)

<P><STRONG><a name="[97]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[96]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[a2]"></a>_dsqrt</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, dsqrt.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _dsqrt &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>

<P><STRONG><a name="[8c]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[339]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[33a]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[33b]"></a>__decompress1</STRONG> (Thumb, 86 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[a4]"></a>AHRS_asinf</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, AHRS_middleware.o(i.AHRS_asinf))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = AHRS_asinf &rArr; __hardfp_asinf &rArr; sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asinf
</UL>
<BR>[Called By]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_angle
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_init
</UL>

<P><STRONG><a name="[a6]"></a>AHRS_atan2f</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, AHRS_middleware.o(i.AHRS_atan2f))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = AHRS_atan2f &rArr; __hardfp_atan2f
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>
<BR>[Called By]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_angle
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_init
</UL>

<P><STRONG><a name="[a8]"></a>AHRS_cosf</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, AHRS_middleware.o(i.AHRS_cosf))
<BR><BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cos_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_init
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;angle_to_quat
</UL>

<P><STRONG><a name="[ab]"></a>AHRS_get_height</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, AHRS_middleware.o(i.AHRS_get_height))
<BR><BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_init
</UL>

<P><STRONG><a name="[ac]"></a>AHRS_get_latitude</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, AHRS_middleware.o(i.AHRS_get_latitude))
<BR><BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_init
</UL>

<P><STRONG><a name="[aa]"></a>AHRS_init</STRONG> (Thumb, 626 bytes, Stack size 80 bytes, ahrs.o(i.AHRS_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = AHRS_init &rArr; angle_to_quat
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_sinf
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_invSqrt
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_get_latitude
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_get_height
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_cosf
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_atan2f
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_asinf
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;angle_to_quat
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_task
</UL>

<P><STRONG><a name="[ae]"></a>AHRS_invSqrt</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, AHRS_middleware.o(i.AHRS_invSqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = AHRS_invSqrt &rArr; __hardfp_sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrtf
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_init
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;quat_normalization
<LI><a href="#[27e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_update_kp_ki
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_comple_filter
</UL>

<P><STRONG><a name="[ad]"></a>AHRS_sinf</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, AHRS_middleware.o(i.AHRS_sinf))
<BR><BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_sin_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_init
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;angle_to_quat
</UL>

<P><STRONG><a name="[b2]"></a>AHRS_update</STRONG> (Thumb, 314 bytes, Stack size 136 bytes, ahrs.o(i.AHRS_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = AHRS_update &rArr; accel_comple_filter &rArr; accel_update_kp_ki &rArr; AHRS_invSqrt &rArr; __hardfp_sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;update_w
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;quat_normalization
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_comple_filter
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_task
</UL>

<P><STRONG><a name="[b7]"></a>Angle_error</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, chassis_task.o(i.Angle_error))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = Angle_error &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
</UL>
<BR>[Called By]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_switch
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_key_Contorl
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_Contorl
</UL>

<P><STRONG><a name="[bb]"></a>BMI088_ACCEL_NS_H</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, BMI088Middleware.o(i.BMI088_ACCEL_NS_H))
<BR><BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_read
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_accel_self_test
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_accel_init
</UL>

<P><STRONG><a name="[bd]"></a>BMI088_ACCEL_NS_L</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, BMI088Middleware.o(i.BMI088_ACCEL_NS_L))
<BR><BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_read
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_accel_self_test
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_accel_init
</UL>

<P><STRONG><a name="[c5]"></a>BMI088_GPIO_init</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, BMI088Middleware.o(i.BMI088_GPIO_init))
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_init
</UL>

<P><STRONG><a name="[be]"></a>BMI088_GYRO_NS_H</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, BMI088Middleware.o(i.BMI088_GYRO_NS_H))
<BR><BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_read
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_gyro_self_test
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_gyro_init
</UL>

<P><STRONG><a name="[bf]"></a>BMI088_GYRO_NS_L</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, BMI088Middleware.o(i.BMI088_GYRO_NS_L))
<BR><BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_read
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_gyro_self_test
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_gyro_init
</UL>

<P><STRONG><a name="[1da]"></a>BMI088_accel_read_over</STRONG> (Thumb, 126 bytes, Stack size 12 bytes, BMI088driver.o(i.BMI088_accel_read_over))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = BMI088_accel_read_over
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_task
</UL>

<P><STRONG><a name="[c6]"></a>BMI088_com_init</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, BMI088Middleware.o(i.BMI088_com_init))
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_init
</UL>

<P><STRONG><a name="[c0]"></a>BMI088_delay_ms</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, BMI088Middleware.o(i.BMI088_delay_ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = BMI088_delay_ms &rArr; osDelay &rArr; vTaskDelay &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_gyro_self_test
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_gyro_init
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_accel_self_test
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_accel_init
</UL>

<P><STRONG><a name="[c2]"></a>BMI088_delay_us</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, BMI088Middleware.o(i.BMI088_delay_us))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = BMI088_delay_us &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_gyro_self_test
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_gyro_init
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_accel_self_test
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_accel_init
</UL>

<P><STRONG><a name="[1d9]"></a>BMI088_gyro_read_over</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, BMI088driver.o(i.BMI088_gyro_read_over))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = BMI088_gyro_read_over
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_task
</UL>

<P><STRONG><a name="[c4]"></a>BMI088_init</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, BMI088driver.o(i.BMI088_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = BMI088_init &rArr; bmi088_accel_self_test &rArr; BMI088_read_muli_reg &rArr; BMI088_read_write_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_gyro_self_test
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_gyro_init
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_accel_self_test
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_accel_init
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_com_init
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_GPIO_init
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_task
</UL>

<P><STRONG><a name="[cb]"></a>BMI088_read</STRONG> (Thumb, 342 bytes, Stack size 32 bytes, BMI088driver.o(i.BMI088_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = BMI088_read &rArr; BMI088_read_muli_reg &rArr; BMI088_read_write_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_read_muli_reg
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_read_write_byte
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_GYRO_NS_L
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_GYRO_NS_H
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_ACCEL_NS_L
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_ACCEL_NS_H
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_task
</UL>

<P><STRONG><a name="[cc]"></a>BMI088_read_write_byte</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, BMI088Middleware.o(i.BMI088_read_write_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = BMI088_read_write_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_read
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_accel_self_test
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_accel_init
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_write_single_reg
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_read_single_reg
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_read_muli_reg
</UL>

<P><STRONG><a name="[1db]"></a>BMI088_temperature_read_over</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, BMI088driver.o(i.BMI088_temperature_read_over))
<BR><BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_task
</UL>

<P><STRONG><a name="[21]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.BusFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.CAN1_RX0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = CAN1_RX0_IRQHandler &rArr; HAL_CAN_IRQHandler &rArr; HAL_CAN_RxFifo0MsgPendingCallback &rArr; __aeabi_ui2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.CAN2_RX0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = CAN2_RX0_IRQHandler &rArr; HAL_CAN_IRQHandler &rArr; HAL_CAN_RxFifo0MsgPendingCallback &rArr; __aeabi_ui2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[d2]"></a>CAN_cmd_RGB</STRONG> (Thumb, 72 bytes, Stack size 32 bytes, CAN_receive.o(i.CAN_cmd_RGB))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = CAN_cmd_RGB &rArr; HAL_CAN_AddTxMessage
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_AddTxMessage
</UL>
<BR>[Called By]<UL><LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_oncar
</UL>

<P><STRONG><a name="[d4]"></a>CAN_cmd_Trigger</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, CAN_receive.o(i.CAN_cmd_Trigger))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = CAN_cmd_Trigger &rArr; HAL_CAN_AddTxMessage
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_AddTxMessage
</UL>
<BR>[Called By]<UL><LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SHOOT_CANbusCtrlMotor
</UL>

<P><STRONG><a name="[d5]"></a>CAN_cmd_chassis</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, CAN_receive.o(i.CAN_cmd_chassis))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = CAN_cmd_chassis &rArr; HAL_CAN_AddTxMessage
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_AddTxMessage
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CHASSIS_CANSend
</UL>

<P><STRONG><a name="[d6]"></a>CAN_cmd_chassis_reset_ID</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, CAN_receive.o(i.CAN_cmd_chassis_reset_ID))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = CAN_cmd_chassis_reset_ID &rArr; HAL_CAN_AddTxMessage
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_AddTxMessage
</UL>
<BR>[Called By]<UL><LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RC_cmd_to_calibrate
</UL>

<P><STRONG><a name="[d7]"></a>CAN_cmd_gimbal_pitch</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, CAN_receive.o(i.CAN_cmd_gimbal_pitch))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = CAN_cmd_gimbal_pitch &rArr; HAL_CAN_AddTxMessage
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_AddTxMessage
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_task
</UL>

<P><STRONG><a name="[d8]"></a>CAN_cmd_gimbal_shoot</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, CAN_receive.o(i.CAN_cmd_gimbal_shoot))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = CAN_cmd_gimbal_shoot &rArr; HAL_CAN_AddTxMessage
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_AddTxMessage
</UL>
<BR>[Called By]<UL><LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SHOOT_CANbusCtrlMotor
</UL>

<P><STRONG><a name="[d9]"></a>CAN_cmd_gimbal_yaw</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, CAN_receive.o(i.CAN_cmd_gimbal_yaw))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = CAN_cmd_gimbal_yaw &rArr; HAL_CAN_AddTxMessage
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_AddTxMessage
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_task
</UL>

<P><STRONG><a name="[da]"></a>CAN_cmd_power</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, CAN_receive.o(i.CAN_cmd_power))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = CAN_cmd_power &rArr; HAL_CAN_AddTxMessage
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_AddTxMessage
</UL>
<BR>[Called By]<UL><LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;power_data_sent
</UL>

<P><STRONG><a name="[e]"></a>CDC_Receive_FS</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, usbd_cdc_if.o(i.CDC_Receive_FS))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = CDC_Receive_FS &rArr; USBD_CDC_ReceivePacket &rArr; USBD_LL_PrepareReceive &rArr; HAL_PCD_EP_Receive &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CDC_SetRxBuffer
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CDC_ReceivePacket
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_read_data
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_cdc_if.o(.data)
</UL>
<P><STRONG><a name="[df]"></a>CDC_Transmit_FS</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, usbd_cdc_if.o(i.CDC_Transmit_FS))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = CDC_Transmit_FS &rArr; USBD_CDC_TransmitPacket &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CDC_TransmitPacket
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CDC_SetTxBuffer
</UL>
<BR>[Called By]<UL><LI><a href="#[310]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_send_data
</UL>

<P><STRONG><a name="[e1]"></a>CHASSIS_CANSend</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, chassis_task.o(i.CHASSIS_CANSend))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = CHASSIS_CANSend &rArr; CAN_cmd_chassis &rArr; HAL_CAN_AddTxMessage
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN_cmd_chassis
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;toe_is_error
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chassis_task
</UL>

<P><STRONG><a name="[e9]"></a>CHASSIS_UPUP_Mode_Ctrl</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, chassis_task.o(i.CHASSIS_UPUP_Mode_Ctrl))
<BR><BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Key_Ctrl
</UL>

<P><STRONG><a name="[136]"></a>CHASSIS_UpdateMotorAngle</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, chassis_task.o(i.CHASSIS_UpdateMotorAngle))
<BR><BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_RxFifo0MsgPendingCallback
</UL>

<P><STRONG><a name="[138]"></a>CHASSIS_UpdateMotorCur</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, chassis_task.o(i.CHASSIS_UpdateMotorCur))
<BR><BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_RxFifo0MsgPendingCallback
</UL>

<P><STRONG><a name="[137]"></a>CHASSIS_UpdateMotorSpeed</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, chassis_task.o(i.CHASSIS_UpdateMotorSpeed))
<BR><BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_RxFifo0MsgPendingCallback
</UL>

<P><STRONG><a name="[e3]"></a>Chassis_Init</STRONG> (Thumb, 338 bytes, Stack size 40 bytes, chassis_task.o(i.Chassis_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Chassis_Init &rArr; chassis_feedback_update
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_init
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;first_order_filter_init
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chassis_feedback_update
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chassis_task
</UL>

<P><STRONG><a name="[e7]"></a>Chassis_Key_Ctrl</STRONG> (Thumb, 96 bytes, Stack size 24 bytes, chassis_task.o(i.Chassis_Key_Ctrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = Chassis_Key_Ctrl &rArr; Chassis_NORMAL_Mode_Ctrl &rArr; Chassis_Keyboard_Move_Calculate &rArr; Chassis_Key_MoveRamp &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_NORMAL_Mode_Ctrl
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Keyboard_Move_Calculate
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CHASSIS_UPUP_Mode_Ctrl
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chassis_task
</UL>

<P><STRONG><a name="[eb]"></a>Chassis_Key_MoveRamp</STRONG> (Thumb, 136 bytes, Stack size 40 bytes, chassis_task.o(i.Chassis_Key_MoveRamp))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = Chassis_Key_MoveRamp &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;constrain_float
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Keyboard_Move_Calculate
</UL>

<P><STRONG><a name="[ea]"></a>Chassis_Keyboard_Move_Calculate</STRONG> (Thumb, 1406 bytes, Stack size 80 bytes, chassis_task.o(i.Chassis_Keyboard_Move_Calculate))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = Chassis_Keyboard_Move_Calculate &rArr; Chassis_Key_MoveRamp &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;first_order_filter_cali
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;constrain_float
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Key_MoveRamp
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_NORMAL_Mode_Ctrl
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Key_Ctrl
</UL>

<P><STRONG><a name="[f3]"></a>Chassis_MotorOutput</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, chassis_task.o(i.Chassis_MotorOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Chassis_MotorOutput &rArr; Chassis_Motor_Key_PID
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Motor_Key_PID
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chassis_task
</UL>

<P><STRONG><a name="[f4]"></a>Chassis_Motor_Key_PID</STRONG> (Thumb, 298 bytes, Stack size 32 bytes, chassis_task.o(i.Chassis_Motor_Key_PID))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Chassis_Motor_Key_PID
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;constrain_float
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_MotorOutput
</UL>

<P><STRONG><a name="[f5]"></a>Chassis_Motor_Speed_PID</STRONG> (Thumb, 386 bytes, Stack size 40 bytes, chassis_task.o(i.Chassis_Motor_Speed_PID))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = Chassis_Motor_Speed_PID &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_calc
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chassis_task
</UL>

<P><STRONG><a name="[f7]"></a>Chassis_Motor_Speed_PID_KEY</STRONG> (Thumb, 90 bytes, Stack size 24 bytes, chassis_task.o(i.Chassis_Motor_Speed_PID_KEY))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Chassis_Motor_Speed_PID_KEY
</UL>
<BR>[Calls]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_calc
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chassis_task
</UL>

<P><STRONG><a name="[f8]"></a>Chassis_Mouse_Move_Calculate</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, chassis_task.o(i.Chassis_Mouse_Move_Calculate))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Chassis_Mouse_Move_Calculate &rArr; GIMBAL_GetOffsetAngle
</UL>
<BR>[Calls]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_calc
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;constrain_float
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GIMBAL_GetOffsetAngle
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_NORMAL_Mode_Ctrl
</UL>

<P><STRONG><a name="[e8]"></a>Chassis_NORMAL_Mode_Ctrl</STRONG> (Thumb, 102 bytes, Stack size 8 bytes, chassis_task.o(i.Chassis_NORMAL_Mode_Ctrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = Chassis_NORMAL_Mode_Ctrl &rArr; Chassis_Keyboard_Move_Calculate &rArr; Chassis_Key_MoveRamp &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Mouse_Move_Calculate
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Keyboard_Move_Calculate
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Key_Ctrl
</UL>

<P><STRONG><a name="[fa]"></a>Chassis_Omni_Move_Calculate</STRONG> (Thumb, 806 bytes, Stack size 24 bytes, chassis_task.o(i.Chassis_Omni_Move_Calculate))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = Chassis_Omni_Move_Calculate &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chassis_move
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chassis_task
</UL>

<P><STRONG><a name="[fc]"></a>Chassis_Rc_Control</STRONG> (Thumb, 236 bytes, Stack size 24 bytes, chassis_task.o(i.Chassis_Rc_Control))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Chassis_Rc_Control &rArr; first_order_filter_cali
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;first_order_filter_cali
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chassis_task
</UL>

<P><STRONG><a name="[fd]"></a>Chassis_Set_Contorl</STRONG> (Thumb, 752 bytes, Stack size 40 bytes, chassis_task.o(i.Chassis_Set_Contorl))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = Chassis_Set_Contorl &rArr; Angle_error &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_calc
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_sin_f32
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chassis_follow_gimbal_yaw_control
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;loop_fp32_constrain
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fp32_constrain
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cos_f32
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Angle_error
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chassis_task
</UL>

<P><STRONG><a name="[101]"></a>Chassis_Set_Mode</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, chassis_task.o(i.Chassis_Set_Mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Chassis_Set_Mode
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Reset_Fric
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chassis_task
</UL>

<P><STRONG><a name="[103]"></a>Chassis_Set_key_Contorl</STRONG> (Thumb, 592 bytes, Stack size 24 bytes, chassis_task.o(i.Chassis_Set_key_Contorl))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = Chassis_Set_key_Contorl &rArr; Angle_error &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_calc
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_sin_f32
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chassis_follow_gimbal_yaw_control
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;loop_fp32_constrain
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fp32_constrain
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cos_f32
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Angle_error
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chassis_task
</UL>

<P><STRONG><a name="[56]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, bsp_i2c.o(i.DMA1_Stream7_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA1_Stream7_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA2_Stream1_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 1204 bytes, Stack size 40 bytes, INS_task.o(i.DMA2_Stream2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = DMA2_Stream2_IRQHandler &rArr; imu_cmd_spi_dma &rArr; SPI1_DMA_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_cmd_spi_dma
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[6b]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DMA2_Stream5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA2_Stream5_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[6c]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DMA2_Stream6_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA2_Stream6_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[6d]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DMA2_Stream7_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA2_Stream7_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.EXTI0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = EXTI0_IRQHandler &rArr; HAL_GPIO_EXTI_IRQHandler &rArr; HAL_GPIO_EXTI_Callback &rArr; imu_cmd_spi_dma &rArr; SPI1_DMA_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.EXTI3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = EXTI3_IRQHandler &rArr; HAL_GPIO_EXTI_IRQHandler &rArr; HAL_GPIO_EXTI_Callback &rArr; imu_cmd_spi_dma &rArr; SPI1_DMA_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.EXTI4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = EXTI4_IRQHandler &rArr; HAL_GPIO_EXTI_IRQHandler &rArr; HAL_GPIO_EXTI_Callback &rArr; imu_cmd_spi_dma &rArr; SPI1_DMA_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.EXTI9_5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = EXTI9_5_IRQHandler &rArr; HAL_GPIO_EXTI_IRQHandler &rArr; HAL_GPIO_EXTI_Callback &rArr; imu_cmd_spi_dma &rArr; SPI1_DMA_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[15b]"></a>Error_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, main.o(i.Error_Handler))
<BR><BR>[Called By]<UL><LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Init
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_CRC_Init
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_CAN2_Init
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_CAN1_Init
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC3_Init
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART6_UART_Init
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM8_Init
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM5_Init
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM10_Init
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI1_Init
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RTC_Init
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RNG_Init
<LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C3_Init
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C2_Init
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USB_DEVICE_Init
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_task
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_ResetCallback
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[281]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adcx_get_chx_value
</UL>

<P><STRONG><a name="[146]"></a>FLASH_Erase_Sector</STRONG> (Thumb, 76 bytes, Stack size 0 bytes, stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector))
<BR><BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASHEx_Erase
</UL>

<P><STRONG><a name="[147]"></a>FLASH_FlushCaches</STRONG> (Thumb, 80 bytes, Stack size 0 bytes, stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches))
<BR><BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASHEx_Erase
</UL>

<P><STRONG><a name="[107]"></a>FLASH_WaitForLastOperation</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = FLASH_WaitForLastOperation
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_SetErrorCode
</UL>
<BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASHEx_Erase
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASH_Program
</UL>

<P><STRONG><a name="[111]"></a>FRIC_RcSwitch</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, shoot_task.o(i.FRIC_RcSwitch))
<BR><BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fric_mode
</UL>

<P><STRONG><a name="[10a]"></a>Fric_Key_Ctrl</STRONG> (Thumb, 94 bytes, Stack size 8 bytes, shoot_task.o(i.Fric_Key_Ctrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Fric_Key_Ctrl &rArr; Set_Fric_Speed &rArr; Friction_SpeedFIX &rArr; SpeedAdapt
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Reset_Fric
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Fric_Speed
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shoot_task
</UL>

<P><STRONG><a name="[10c]"></a>Fric_Power_Change</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, shoot_task.o(i.Fric_Power_Change))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Fric_Power_Change
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shoot_task
</UL>

<P><STRONG><a name="[10d]"></a>Fric_SpeedLoop</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, shoot_task.o(i.Fric_SpeedLoop))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = Fric_SpeedLoop &rArr; New_PID_cail &rArr; activation_function &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;New_PID_cail
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fric_Speed_PID_Calculate
</UL>

<P><STRONG><a name="[10f]"></a>Fric_Speed_PID_Calculate</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, shoot_task.o(i.Fric_Speed_PID_Calculate))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = Fric_Speed_PID_Calculate &rArr; Fric_SpeedLoop &rArr; New_PID_cail &rArr; activation_function &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fric_SpeedLoop
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shoot_task
</UL>

<P><STRONG><a name="[139]"></a>Fric_UpdateMotorSpeed</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, shoot_task.o(i.Fric_UpdateMotorSpeed))
<BR><BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_RxFifo0MsgPendingCallback
</UL>

<P><STRONG><a name="[13a]"></a>Fric_UpdateMotorTemperate</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, shoot_task.o(i.Fric_UpdateMotorTemperate))
<BR><BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_RxFifo0MsgPendingCallback
</UL>

<P><STRONG><a name="[110]"></a>Fric_mode</STRONG> (Thumb, 108 bytes, Stack size 4 bytes, shoot_task.o(i.Fric_mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = Fric_mode
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Friction_Ramp
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FRIC_RcSwitch
</UL>
<BR>[Called By]<UL><LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SHOOT_MIDF_HIGHTS_Ctrl
</UL>

<P><STRONG><a name="[1e1]"></a>Fric_open_off</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, shoot_task.o(i.Fric_open_off))
<BR><BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_update_g_Dynamic_2
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_oncar
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SHOOT_NORMAL_Ctrl
</UL>

<P><STRONG><a name="[112]"></a>Friction_Ramp</STRONG> (Thumb, 82 bytes, Stack size 0 bytes, shoot_task.o(i.Friction_Ramp))
<BR><BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fric_mode
</UL>

<P><STRONG><a name="[113]"></a>Friction_SpeedFIX</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, shoot_task.o(i.Friction_SpeedFIX))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Friction_SpeedFIX &rArr; SpeedAdapt
</UL>
<BR>[Calls]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Temp_Fix_25S
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SpeedAdapt
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_shoot_speed
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Fric_Speed
</UL>

<P><STRONG><a name="[117]"></a>GIMBAL_AUTO_Mode_Ctrl</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, gimbal_task.o(i.GIMBAL_AUTO_Mode_Ctrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = GIMBAL_AUTO_Mode_Ctrl &rArr; Vision_Error_Angle_YAW &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vision_If_UpDate
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vision_Error_Angle_YAW
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vision_Error_Angle_PITCH
</UL>
<BR>[Called By]<UL><LI><a href="#[2b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_set_control
</UL>

<P><STRONG><a name="[f9]"></a>GIMBAL_GetOffsetAngle</STRONG> (Thumb, 130 bytes, Stack size 16 bytes, gimbal_task.o(i.GIMBAL_GetOffsetAngle))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = GIMBAL_GetOffsetAngle
</UL>
<BR>[Called By]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_switch
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Mouse_Move_Calculate
</UL>

<P><STRONG><a name="[1e5]"></a>HAL_ADC_ConfigChannel</STRONG> (Thumb, 326 bytes, Stack size 20 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_ADC_ConfigChannel
</UL>
<BR>[Called By]<UL><LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC3_Init
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
<LI><a href="#[281]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adcx_get_chx_value
</UL>

<P><STRONG><a name="[283]"></a>HAL_ADC_GetValue</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_GetValue))
<BR><BR>[Called By]<UL><LI><a href="#[281]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adcx_get_chx_value
</UL>

<P><STRONG><a name="[11d]"></a>HAL_ADC_Init</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_ADC_Init &rArr; HAL_ADC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC3_Init
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
</UL>

<P><STRONG><a name="[11e]"></a>HAL_ADC_MspInit</STRONG> (Thumb, 108 bytes, Stack size 32 bytes, adc.o(i.HAL_ADC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_ADC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
</UL>

<P><STRONG><a name="[121]"></a>HAL_ADC_PollForConversion</STRONG> (Thumb, 162 bytes, Stack size 24 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_ADC_PollForConversion
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[281]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adcx_get_chx_value
</UL>

<P><STRONG><a name="[282]"></a>HAL_ADC_Start</STRONG> (Thumb, 234 bytes, Stack size 12 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = HAL_ADC_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[281]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adcx_get_chx_value
</UL>

<P><STRONG><a name="[297]"></a>HAL_CAN_ActivateNotification</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, stm32f4xx_hal_can.o(i.HAL_CAN_ActivateNotification))
<BR><BR>[Called By]<UL><LI><a href="#[295]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_filter_init
</UL>

<P><STRONG><a name="[d3]"></a>HAL_CAN_AddTxMessage</STRONG> (Thumb, 242 bytes, Stack size 20 bytes, stm32f4xx_hal_can.o(i.HAL_CAN_AddTxMessage))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_CAN_AddTxMessage
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN_cmd_power
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN_cmd_gimbal_yaw
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN_cmd_gimbal_shoot
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN_cmd_gimbal_pitch
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN_cmd_chassis_reset_ID
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN_cmd_chassis
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN_cmd_Trigger
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN_cmd_RGB
</UL>

<P><STRONG><a name="[296]"></a>HAL_CAN_ConfigFilter</STRONG> (Thumb, 266 bytes, Stack size 8 bytes, stm32f4xx_hal_can.o(i.HAL_CAN_ConfigFilter))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_CAN_ConfigFilter
</UL>
<BR>[Called By]<UL><LI><a href="#[295]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_filter_init
</UL>

<P><STRONG><a name="[12e]"></a>HAL_CAN_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_can.o(i.HAL_CAN_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>

<P><STRONG><a name="[133]"></a>HAL_CAN_GetRxMessage</STRONG> (Thumb, 260 bytes, Stack size 12 bytes, stm32f4xx_hal_can.o(i.HAL_CAN_GetRxMessage))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = HAL_CAN_GetRxMessage
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_RxFifo0MsgPendingCallback
</UL>

<P><STRONG><a name="[d1]"></a>HAL_CAN_IRQHandler</STRONG> (Thumb, 508 bytes, Stack size 40 bytes, stm32f4xx_hal_can.o(i.HAL_CAN_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = HAL_CAN_IRQHandler &rArr; HAL_CAN_RxFifo0MsgPendingCallback &rArr; __aeabi_ui2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_WakeUpFromRxMsgCallback
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_TxMailbox2CompleteCallback
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_TxMailbox2AbortCallback
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_TxMailbox1CompleteCallback
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_TxMailbox1AbortCallback
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_TxMailbox0CompleteCallback
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_TxMailbox0AbortCallback
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_SleepCallback
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_RxFifo1MsgPendingCallback
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_RxFifo1FullCallback
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_RxFifo0FullCallback
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_ErrorCallback
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_RxFifo0MsgPendingCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN2_RX0_IRQHandler
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_RX0_IRQHandler
</UL>

<P><STRONG><a name="[12f]"></a>HAL_CAN_Init</STRONG> (Thumb, 338 bytes, Stack size 16 bytes, stm32f4xx_hal_can.o(i.HAL_CAN_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = HAL_CAN_Init &rArr; HAL_CAN_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_CAN2_Init
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_CAN1_Init
</UL>

<P><STRONG><a name="[130]"></a>HAL_CAN_MspInit</STRONG> (Thumb, 230 bytes, Stack size 48 bytes, can.o(i.HAL_CAN_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_CAN_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_Init
</UL>

<P><STRONG><a name="[128]"></a>HAL_CAN_RxFifo0FullCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_can.o(i.HAL_CAN_RxFifo0FullCallback))
<BR><BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>

<P><STRONG><a name="[129]"></a>HAL_CAN_RxFifo0MsgPendingCallback</STRONG> (Thumb, 1168 bytes, Stack size 80 bytes, CAN_receive.o(i.HAL_CAN_RxFifo0MsgPendingCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = HAL_CAN_RxFifo0MsgPendingCallback &rArr; __aeabi_ui2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_GetRxMessage
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pm01_response_handle
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;detect_hook
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_UpdateMotorSpeed
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_UpdateMotorAngle
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fric_UpdateMotorTemperate
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fric_UpdateMotorSpeed
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CHASSIS_UpdateMotorSpeed
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CHASSIS_UpdateMotorCur
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CHASSIS_UpdateMotorAngle
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>

<P><STRONG><a name="[12a]"></a>HAL_CAN_RxFifo1FullCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_can.o(i.HAL_CAN_RxFifo1FullCallback))
<BR><BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>

<P><STRONG><a name="[12b]"></a>HAL_CAN_RxFifo1MsgPendingCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_can.o(i.HAL_CAN_RxFifo1MsgPendingCallback))
<BR><BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>

<P><STRONG><a name="[12c]"></a>HAL_CAN_SleepCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_can.o(i.HAL_CAN_SleepCallback))
<BR><BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>

<P><STRONG><a name="[13d]"></a>HAL_CAN_Start</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, stm32f4xx_hal_can.o(i.HAL_CAN_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_CAN_Start
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[295]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_filter_init
</UL>

<P><STRONG><a name="[123]"></a>HAL_CAN_TxMailbox0AbortCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox0AbortCallback))
<BR><BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>

<P><STRONG><a name="[122]"></a>HAL_CAN_TxMailbox0CompleteCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox0CompleteCallback))
<BR><BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>

<P><STRONG><a name="[125]"></a>HAL_CAN_TxMailbox1AbortCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox1AbortCallback))
<BR><BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>

<P><STRONG><a name="[124]"></a>HAL_CAN_TxMailbox1CompleteCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox1CompleteCallback))
<BR><BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>

<P><STRONG><a name="[127]"></a>HAL_CAN_TxMailbox2AbortCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox2AbortCallback))
<BR><BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>

<P><STRONG><a name="[126]"></a>HAL_CAN_TxMailbox2CompleteCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_can.o(i.HAL_CAN_TxMailbox2CompleteCallback))
<BR><BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>

<P><STRONG><a name="[12d]"></a>HAL_CAN_WakeUpFromRxMsgCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_can.o(i.HAL_CAN_WakeUpFromRxMsgCallback))
<BR><BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_IRQHandler
</UL>

<P><STRONG><a name="[13e]"></a>HAL_CRC_Init</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, stm32f4xx_hal_crc.o(i.HAL_CRC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_CRC_Init &rArr; HAL_CRC_MspInit
</UL>
<BR>[Calls]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CRC_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_CRC_Init
</UL>

<P><STRONG><a name="[13f]"></a>HAL_CRC_MspInit</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, crc.o(i.HAL_CRC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_CRC_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CRC_Init
</UL>

<P><STRONG><a name="[1c8]"></a>HAL_DMA_Abort_IT</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT))
<BR><BR>[Called By]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[104]"></a>HAL_DMA_IRQHandler</STRONG> (Thumb, 460 bytes, Stack size 32 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_DMA_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream7_IRQHandler
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream6_IRQHandler
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream5_IRQHandler
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream1_IRQHandler
</UL>

<P><STRONG><a name="[140]"></a>HAL_DMA_Init</STRONG> (Thumb, 208 bytes, Stack size 24 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_DMA_Init &rArr; DMA_CheckFifoParam
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CheckFifoParam
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CalcBaseAndBitshift
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
</UL>

<P><STRONG><a name="[143]"></a>HAL_Delay</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, stm32f4xx_hal.o(i.HAL_Delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_SetCurrentMode
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DevDisconnect
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DevConnect
</UL>

<P><STRONG><a name="[144]"></a>HAL_FLASHEx_Erase</STRONG> (Thumb, 150 bytes, Stack size 32 bytes, stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_FLASHEx_Erase &rArr; FLASH_WaitForLastOperation
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_MassErase
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_FlushCaches
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Erase_Sector
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_WaitForLastOperation
</UL>
<BR>[Called By]<UL><LI><a href="#[28c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_erase_address
</UL>

<P><STRONG><a name="[29e]"></a>HAL_FLASH_Lock</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock))
<BR><BR>[Called By]<UL><LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_write_single_address
<LI><a href="#[28c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_erase_address
</UL>

<P><STRONG><a name="[148]"></a>HAL_FLASH_Program</STRONG> (Thumb, 116 bytes, Stack size 32 bytes, stm32f4xx_hal_flash.o(i.HAL_FLASH_Program))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_FLASH_Program &rArr; FLASH_WaitForLastOperation
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_WaitForLastOperation
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Program_Word
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Program_HalfWord
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Program_DoubleWord
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_Program_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_write_single_address
</UL>

<P><STRONG><a name="[29d]"></a>HAL_FLASH_Unlock</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock))
<BR><BR>[Called By]<UL><LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_write_single_address
<LI><a href="#[28c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_erase_address
</UL>

<P><STRONG><a name="[14d]"></a>HAL_GPIO_EXTI_Callback</STRONG> (Thumb, 142 bytes, Stack size 8 bytes, INS_task.o(i.HAL_GPIO_EXTI_Callback))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_GPIO_EXTI_Callback &rArr; imu_cmd_spi_dma &rArr; SPI1_DMA_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskNotifyGiveFromISR
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_cmd_spi_dma
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;detect_hook
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_IRQHandler
</UL>

<P><STRONG><a name="[106]"></a>HAL_GPIO_EXTI_IRQHandler</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_GPIO_EXTI_IRQHandler &rArr; HAL_GPIO_EXTI_Callback &rArr; imu_cmd_spi_dma &rArr; SPI1_DMA_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_Callback
</UL>
<BR>[Called By]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI9_5_IRQHandler
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI4_IRQHandler
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI3_IRQHandler
<LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI0_IRQHandler
</UL>

<P><STRONG><a name="[120]"></a>HAL_GPIO_Init</STRONG> (Thumb, 406 bytes, Stack size 40 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_MspInit
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_MspPostInit
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_MspInit
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
</UL>

<P><STRONG><a name="[bc]"></a>HAL_GPIO_WritePin</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin))
<BR><BR>[Called By]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_cmd_spi_dma
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream2_IRQHandler
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_GYRO_NS_L
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_GYRO_NS_H
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_ACCEL_NS_L
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_ACCEL_NS_H
<LI><a href="#[2c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ist8310_RST_L
<LI><a href="#[2c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ist8310_RST_H
</UL>

<P><STRONG><a name="[108]"></a>HAL_GetTick</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal.o(i.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_Init
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_PollForConversion
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_WaitForLastOperation
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_Start
<LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_EnterInitMode
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_WaitForSynchro
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Read
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnRXNEFlagUntilTimeout
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnMasterAddressFlagUntilTimeout
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
</UL>

<P><STRONG><a name="[150]"></a>HAL_I2C_Init</STRONG> (Thumb, 372 bytes, Stack size 16 bytes, stm32f4xx_hal_i2c.o(i.HAL_I2C_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C3_Init
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C2_Init
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
</UL>

<P><STRONG><a name="[153]"></a>HAL_I2C_Mem_Read</STRONG> (Thumb, 638 bytes, Stack size 64 bytes, stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnRXNEFlagUntilTimeout
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryRead
</UL>
<BR>[Called By]<UL><LI><a href="#[2bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ist8310_IIC_read_single_reg
</UL>

<P><STRONG><a name="[157]"></a>HAL_I2C_Mem_Write</STRONG> (Thumb, 316 bytes, Stack size 64 bytes, stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
</UL>
<BR>[Called By]<UL><LI><a href="#[2c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ist8310_IIC_write_single_reg
</UL>

<P><STRONG><a name="[151]"></a>HAL_I2C_MspInit</STRONG> (Thumb, 348 bytes, Stack size 56 bytes, i2c.o(i.HAL_I2C_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
</UL>

<P><STRONG><a name="[225]"></a>HAL_IncTick</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_hal.o(i.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[15c]"></a>HAL_Init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, stm32f4xx_hal.o(i.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[15e]"></a>HAL_InitTick</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, stm32f4xx_hal.o(i.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
</UL>

<P><STRONG><a name="[15f]"></a>HAL_MspInit</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, stm32f4xx_hal_msp.o(i.HAL_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_MspInit &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[132]"></a>HAL_NVIC_EnableIRQ</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_MspInit
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_MspInit
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
</UL>

<P><STRONG><a name="[131]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_MspInit
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_MspInit
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
</UL>

<P><STRONG><a name="[15d]"></a>HAL_NVIC_SetPriorityGrouping</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping))
<BR><BR>[Called By]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[184]"></a>HAL_PCDEx_LPM_Callback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_pcd_ex.o(i.HAL_PCDEx_LPM_Callback))
<BR><BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[246]"></a>HAL_PCDEx_SetRxFiFo</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_hal_pcd_ex.o(i.HAL_PCDEx_SetRxFiFo))
<BR><BR>[Called By]<UL><LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Init
</UL>

<P><STRONG><a name="[247]"></a>HAL_PCDEx_SetTxFiFo</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, stm32f4xx_hal_pcd_ex.o(i.HAL_PCDEx_SetTxFiFo))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_PCDEx_SetTxFiFo
</UL>
<BR>[Called By]<UL><LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Init
</UL>

<P><STRONG><a name="[162]"></a>HAL_PCD_ConnectCallback</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, usbd_conf.o(i.HAL_PCD_ConnectCallback))
<BR><BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_DevConnected
</UL>
<BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[164]"></a>HAL_PCD_DataInStageCallback</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, usbd_conf.o(i.HAL_PCD_DataInStageCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_PCD_DataInStageCallback &rArr; USBD_LL_DataInStage &rArr; USBD_CtlReceiveStatus &rArr; USBD_LL_PrepareReceive &rArr; HAL_PCD_EP_Receive &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_DataInStage
</UL>
<BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[166]"></a>HAL_PCD_DataOutStageCallback</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, usbd_conf.o(i.HAL_PCD_DataOutStageCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_PCD_DataOutStageCallback &rArr; USBD_LL_DataOutStage &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_DataOutStage
</UL>
<BR>[Called By]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PCD_EP_OutXfrComplete_int
</UL>

<P><STRONG><a name="[168]"></a>HAL_PCD_DisconnectCallback</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, usbd_conf.o(i.HAL_PCD_DisconnectCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_PCD_DisconnectCallback &rArr; USBD_LL_DevDisconnected
</UL>
<BR>[Calls]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_DevDisconnected
</UL>
<BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[16a]"></a>HAL_PCD_EP_Close</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, stm32f4xx_hal_pcd.o(i.HAL_PCD_EP_Close))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_PCD_EP_Close &rArr; USB_DeactivateEndpoint
</UL>
<BR>[Calls]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DeactivateEndpoint
</UL>
<BR>[Called By]<UL><LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_CloseEP
</UL>

<P><STRONG><a name="[16c]"></a>HAL_PCD_EP_ClrStall</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, stm32f4xx_hal_pcd.o(i.HAL_PCD_EP_ClrStall))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_PCD_EP_ClrStall
</UL>
<BR>[Calls]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_EPClearStall
</UL>
<BR>[Called By]<UL><LI><a href="#[242]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_ClearStallEP
</UL>

<P><STRONG><a name="[245]"></a>HAL_PCD_EP_GetRxCount</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_hal_pcd.o(i.HAL_PCD_EP_GetRxCount))
<BR><BR>[Called By]<UL><LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_GetRxDataSize
</UL>

<P><STRONG><a name="[16e]"></a>HAL_PCD_EP_Open</STRONG> (Thumb, 92 bytes, Stack size 24 bytes, stm32f4xx_hal_pcd.o(i.HAL_PCD_EP_Open))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_PCD_EP_Open &rArr; USB_ActivateEndpoint
</UL>
<BR>[Calls]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_ActivateEndpoint
</UL>
<BR>[Called By]<UL><LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_OpenEP
</UL>

<P><STRONG><a name="[170]"></a>HAL_PCD_EP_Receive</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, stm32f4xx_hal_pcd.o(i.HAL_PCD_EP_Receive))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_PCD_EP_Receive &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_EPStartXfer
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_EP0StartXfer
</UL>
<BR>[Called By]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_PrepareReceive
</UL>

<P><STRONG><a name="[173]"></a>HAL_PCD_EP_SetStall</STRONG> (Thumb, 112 bytes, Stack size 16 bytes, stm32f4xx_hal_pcd.o(i.HAL_PCD_EP_SetStall))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_PCD_EP_SetStall &rArr; USB_EP0_OutStart
</UL>
<BR>[Calls]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_EPSetStall
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_EP0_OutStart
</UL>
<BR>[Called By]<UL><LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_StallEP
</UL>

<P><STRONG><a name="[176]"></a>HAL_PCD_EP_Transmit</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, stm32f4xx_hal_pcd.o(i.HAL_PCD_EP_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_EPStartXfer
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_EP0StartXfer
</UL>
<BR>[Called By]<UL><LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Transmit
</UL>

<P><STRONG><a name="[177]"></a>HAL_PCD_IRQHandler</STRONG> (Thumb, 1006 bytes, Stack size 40 bytes, stm32f4xx_hal_pcd.o(i.HAL_PCD_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = HAL_PCD_IRQHandler &rArr; PCD_EP_OutXfrComplete_int &rArr; HAL_PCD_SetupStageCallback &rArr; USBD_LL_SetupStage &rArr; USBD_StdDevReq &rArr; USBD_GetDescriptor &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_SetTurnaroundTime
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_ReadPacket
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_ReadInterrupts
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_ReadDevOutEPInterrupt
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_ReadDevInEPInterrupt
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_ReadDevAllOutEpInterrupt
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_ReadDevAllInEpInterrupt
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_GetMode
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_GetDevSpeed
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_FlushTxFifo
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_EP0_OutStart
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_ActivateSetup
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCDEx_LPM_Callback
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PCD_WriteEmptyTxFifo
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PCD_EP_OutXfrComplete_int
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PCD_EP_OutSetupPacket_int
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_SuspendCallback
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_SOFCallback
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_ResumeCallback
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_ResetCallback
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_ISOOUTIncompleteCallback
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_ISOINIncompleteCallback
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_DisconnectCallback
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_DataInStageCallback
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_ConnectCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OTG_FS_IRQHandler
</UL>

<P><STRONG><a name="[18c]"></a>HAL_PCD_ISOINIncompleteCallback</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, usbd_conf.o(i.HAL_PCD_ISOINIncompleteCallback))
<BR><BR>[Calls]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_IsoINIncomplete
</UL>
<BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[18d]"></a>HAL_PCD_ISOOUTIncompleteCallback</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, usbd_conf.o(i.HAL_PCD_ISOOUTIncompleteCallback))
<BR><BR>[Calls]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_IsoOUTIncomplete
</UL>
<BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[190]"></a>HAL_PCD_Init</STRONG> (Thumb, 228 bytes, Stack size 64 bytes, stm32f4xx_hal_pcd.o(i.HAL_PCD_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = HAL_PCD_Init &rArr; HAL_PCD_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_SetCurrentMode
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DisableGlobalInt
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DevInit
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DevDisconnect
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_CoreInit
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_MspInit
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Init
</UL>

<P><STRONG><a name="[191]"></a>HAL_PCD_MspInit</STRONG> (Thumb, 122 bytes, Stack size 32 bytes, usbd_conf.o(i.HAL_PCD_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_PCD_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Init
</UL>

<P><STRONG><a name="[189]"></a>HAL_PCD_ResetCallback</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, usbd_conf.o(i.HAL_PCD_ResetCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_PCD_ResetCallback &rArr; USBD_LL_Reset &rArr; USBD_LL_OpenEP &rArr; HAL_PCD_EP_Open &rArr; USB_ActivateEndpoint
</UL>
<BR>[Calls]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_SetSpeed
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Reset
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[181]"></a>HAL_PCD_ResumeCallback</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, usbd_conf.o(i.HAL_PCD_ResumeCallback))
<BR><BR>[Calls]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Resume
</UL>
<BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[18b]"></a>HAL_PCD_SOFCallback</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, usbd_conf.o(i.HAL_PCD_SOFCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_PCD_SOFCallback &rArr; USBD_LL_SOF
</UL>
<BR>[Calls]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_SOF
</UL>
<BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[19c]"></a>HAL_PCD_SetAddress</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, stm32f4xx_hal_pcd.o(i.HAL_PCD_SetAddress))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_PCD_SetAddress
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_SetDevAddress
</UL>
<BR>[Called By]<UL><LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_SetUSBAddress
</UL>

<P><STRONG><a name="[19e]"></a>HAL_PCD_SetupStageCallback</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, usbd_conf.o(i.HAL_PCD_SetupStageCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = HAL_PCD_SetupStageCallback &rArr; USBD_LL_SetupStage &rArr; USBD_StdDevReq &rArr; USBD_GetDescriptor &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_SetupStage
</UL>
<BR>[Called By]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PCD_EP_OutXfrComplete_int
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PCD_EP_OutSetupPacket_int
</UL>

<P><STRONG><a name="[1a0]"></a>HAL_PCD_Start</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, stm32f4xx_hal_pcd.o(i.HAL_PCD_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_PCD_Start &rArr; USB_DevConnect &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_EnableGlobalInt
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DevConnect
</UL>
<BR>[Called By]<UL><LI><a href="#[24d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Start
</UL>

<P><STRONG><a name="[182]"></a>HAL_PCD_SuspendCallback</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, usbd_conf.o(i.HAL_PCD_SuspendCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_PCD_SuspendCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Suspend
</UL>
<BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[1a4]"></a>HAL_RCCEx_PeriphCLKConfig</STRONG> (Thumb, 306 bytes, Stack size 32 bytes, stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_RCCEx_PeriphCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[1a5]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 308 bytes, Stack size 32 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[187]"></a>HAL_RCC_GetHCLKFreq</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq))
<BR><BR>[Called By]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
</UL>

<P><STRONG><a name="[152]"></a>HAL_RCC_GetPCLK1Freq</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_RCC_GetPCLK1Freq
</UL>
<BR>[Calls]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
</UL>

<P><STRONG><a name="[1a7]"></a>HAL_RCC_GetPCLK2Freq</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_RCC_GetPCLK2Freq
</UL>
<BR>[Calls]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[1a6]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 94 bytes, Stack size 8 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
</UL>

<P><STRONG><a name="[1a8]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 896 bytes, Stack size 40 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_RCC_OscConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[1a9]"></a>HAL_RNG_Init</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, stm32f4xx_hal_rng.o(i.HAL_RNG_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_RNG_Init &rArr; HAL_RNG_MspInit
</UL>
<BR>[Calls]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RNG_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RNG_Init
</UL>

<P><STRONG><a name="[1aa]"></a>HAL_RNG_MspInit</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, rng.o(i.HAL_RNG_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_RNG_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RNG_Init
</UL>

<P><STRONG><a name="[1ab]"></a>HAL_RTC_Init</STRONG> (Thumb, 180 bytes, Stack size 16 bytes, stm32f4xx_hal_rtc.o(i.HAL_RTC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_RTC_Init &rArr; RTC_EnterInitMode
</UL>
<BR>[Calls]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_EnterInitMode
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_WaitForSynchro
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RTC_Init
</UL>

<P><STRONG><a name="[1ac]"></a>HAL_RTC_MspInit</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, rtc.o(i.HAL_RTC_MspInit))
<BR><BR>[Called By]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_Init
</UL>

<P><STRONG><a name="[1ae]"></a>HAL_RTC_WaitForSynchro</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, stm32f4xx_hal_rtc.o(i.HAL_RTC_WaitForSynchro))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_RTC_WaitForSynchro
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_Init
</UL>

<P><STRONG><a name="[1af]"></a>HAL_SPI_Init</STRONG> (Thumb, 130 bytes, Stack size 16 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI1_Init
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_task
</UL>

<P><STRONG><a name="[1b0]"></a>HAL_SPI_MspInit</STRONG> (Thumb, 320 bytes, Stack size 56 bytes, spi.o(i.HAL_SPI_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
</UL>

<P><STRONG><a name="[cf]"></a>HAL_SPI_TransmitReceive</STRONG> (Thumb, 484 bytes, Stack size 40 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_read_write_byte
</UL>

<P><STRONG><a name="[160]"></a>HAL_SYSTICK_Config</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_SYSTICK_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[1f8]"></a>HAL_TIMEx_ConfigBreakDeadTime</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_TIMEx_ConfigBreakDeadTime
</UL>
<BR>[Called By]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM8_Init
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
</UL>

<P><STRONG><a name="[1f7]"></a>HAL_TIMEx_MasterConfigSynchronization</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_TIMEx_MasterConfigSynchronization
</UL>
<BR>[Called By]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM8_Init
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM5_Init
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
</UL>

<P><STRONG><a name="[1b2]"></a>HAL_TIM_Base_Init</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM8_Init
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM5_Init
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM10_Init
</UL>

<P><STRONG><a name="[1b3]"></a>HAL_TIM_Base_MspInit</STRONG> (Thumb, 154 bytes, Stack size 8 bytes, tim.o(i.HAL_TIM_Base_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_TIM_Base_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[1bb]"></a>HAL_TIM_Base_Start</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start))
<BR><BR>[Called By]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_MspPostInit
</UL>

<P><STRONG><a name="[1b5]"></a>HAL_TIM_ConfigClockSource</STRONG> (Thumb, 226 bytes, Stack size 16 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_TIM_ConfigClockSource &rArr; TIM_ETR_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETR_SetConfig
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI2_ConfigInputStage
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI1_ConfigInputStage
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITRx_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM8_Init
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM5_Init
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
</UL>

<P><STRONG><a name="[1ba]"></a>HAL_TIM_MspPostInit</STRONG> (Thumb, 486 bytes, Stack size 48 bytes, tim.o(i.HAL_TIM_MspPostInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_TIM_MspPostInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Start
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Start
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM8_Init
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM5_Init
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM10_Init
</UL>

<P><STRONG><a name="[1bd]"></a>HAL_TIM_PWM_ConfigChannel</STRONG> (Thumb, 218 bytes, Stack size 16 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = HAL_TIM_PWM_ConfigChannel &rArr; TIM_OC2_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2_SetConfig
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC4_SetConfig
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC3_SetConfig
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC1_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM8_Init
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM5_Init
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM10_Init
</UL>

<P><STRONG><a name="[1c2]"></a>HAL_TIM_PWM_Init</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_TIM_PWM_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_MspInit
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM8_Init
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM5_Init
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM10_Init
</UL>

<P><STRONG><a name="[1c3]"></a>HAL_TIM_PWM_MspInit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit))
<BR><BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Init
</UL>

<P><STRONG><a name="[1bc]"></a>HAL_TIM_PWM_Start</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_TIM_PWM_Start &rArr; TIM_CCxChannelCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CCxChannelCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_MspPostInit
</UL>

<P><STRONG><a name="[1c9]"></a>HAL_UART_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAAbortOnError
</UL>

<P><STRONG><a name="[1c5]"></a>HAL_UART_IRQHandler</STRONG> (Thumb, 268 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_UART_IRQHandler &rArr; UART_Receive_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Transmit_IT
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTransmit_IT
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[1cc]"></a>HAL_UART_Init</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART6_UART_Init
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
</UL>

<P><STRONG><a name="[1cd]"></a>HAL_UART_MspInit</STRONG> (Thumb, 360 bytes, Stack size 56 bytes, usart.o(i.HAL_UART_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[229]"></a>HAL_UART_RxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
</UL>

<P><STRONG><a name="[228]"></a>HAL_UART_TxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTransmit_IT
</UL>

<P><STRONG><a name="[1f]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1d1]"></a>INS_cali_gyro</STRONG> (Thumb, 94 bytes, Stack size 12 bytes, INS_task.o(i.INS_cali_gyro))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = INS_cali_gyro
</UL>
<BR>[Calls]<UL><LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_offset_calc
</UL>
<BR>[Called By]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cali_gyro_hook
</UL>

<P><STRONG><a name="[290]"></a>INS_set_cali_gyro</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, INS_task.o(i.INS_set_cali_gyro))
<BR><BR>[Called By]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cali_gyro_hook
</UL>

<P><STRONG><a name="[84]"></a>INS_task</STRONG> (Thumb, 548 bytes, Stack size 16 bytes, INS_task.o(i.INS_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = INS_task &rArr; AHRS_update &rArr; accel_comple_filter &rArr; accel_update_kp_ki &rArr; AHRS_invSqrt &rArr; __hardfp_sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetHandle
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTaskNotifyTake
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pcTaskGetName
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_temp_control
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_cali_slove
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ist8310_init
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_angle
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_DMA_init
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_init
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_temperature_read_over
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_read
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_init
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_gyro_read_over
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_accel_read_over
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_update
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> freertos.o(.constdata)
</UL>
<P><STRONG><a name="[2fb]"></a>JUDGE_ShootNumCount</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, referee.o(i.JUDGE_ShootNumCount))
<BR><BR>[Called By]<UL><LI><a href="#[2fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;referee_data_solve
</UL>

<P><STRONG><a name="[1de]"></a>Key_switch</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, chassis_task.o(i.Key_switch))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = Key_switch &rArr; Angle_error &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GIMBAL_GetOffsetAngle
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Angle_error
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chassis_task
</UL>

<P><STRONG><a name="[1e4]"></a>MX_ADC1_Init</STRONG> (Thumb, 88 bytes, Stack size 32 bytes, adc.o(i.MX_ADC1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = MX_ADC1_Init &rArr; HAL_ADC_Init &rArr; HAL_ADC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1e6]"></a>MX_ADC3_Init</STRONG> (Thumb, 88 bytes, Stack size 32 bytes, adc.o(i.MX_ADC3_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = MX_ADC3_Init &rArr; HAL_ADC_Init &rArr; HAL_ADC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1e7]"></a>MX_CAN1_Init</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, can.o(i.MX_CAN1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = MX_CAN1_Init &rArr; HAL_CAN_Init &rArr; HAL_CAN_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1e8]"></a>MX_CAN2_Init</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, can.o(i.MX_CAN2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = MX_CAN2_Init &rArr; HAL_CAN_Init &rArr; HAL_CAN_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1e9]"></a>MX_CRC_Init</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, crc.o(i.MX_CRC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = MX_CRC_Init &rArr; HAL_CRC_Init &rArr; HAL_CRC_MspInit
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CRC_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1ea]"></a>MX_DMA_Init</STRONG> (Thumb, 168 bytes, Stack size 8 bytes, dma.o(i.MX_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = MX_DMA_Init &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1eb]"></a>MX_FREERTOS_Init</STRONG> (Thumb, 270 bytes, Stack size 344 bytes, freertos.o(i.MX_FREERTOS_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 504<LI>Call Chain = MX_FREERTOS_Init &rArr; osThreadCreate &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osThreadCreate
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1ed]"></a>MX_GPIO_Init</STRONG> (Thumb, 432 bytes, Stack size 56 bytes, gpio.o(i.MX_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = MX_GPIO_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1ee]"></a>MX_I2C1_Init</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, i2c.o(i.MX_I2C1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = MX_I2C1_Init &rArr; HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1ef]"></a>MX_I2C2_Init</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, i2c.o(i.MX_I2C2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = MX_I2C2_Init &rArr; HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1f0]"></a>MX_I2C3_Init</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, i2c.o(i.MX_I2C3_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = MX_I2C3_Init &rArr; HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1f1]"></a>MX_RNG_Init</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, rng.o(i.MX_RNG_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = MX_RNG_Init &rArr; HAL_RNG_Init &rArr; HAL_RNG_MspInit
</UL>
<BR>[Calls]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RNG_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1f2]"></a>MX_RTC_Init</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, rtc.o(i.MX_RTC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = MX_RTC_Init &rArr; HAL_RTC_Init &rArr; RTC_EnterInitMode
</UL>
<BR>[Calls]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1f3]"></a>MX_SPI1_Init</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, spi.o(i.MX_SPI1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = MX_SPI1_Init &rArr; HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1f4]"></a>MX_SPI2_Init</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, spi.o(i.MX_SPI2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = MX_SPI2_Init &rArr; HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1f5]"></a>MX_TIM10_Init</STRONG> (Thumb, 92 bytes, Stack size 40 bytes, tim.o(i.MX_TIM10_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = MX_TIM10_Init &rArr; HAL_TIM_MspPostInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Init
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_MspPostInit
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1f6]"></a>MX_TIM1_Init</STRONG> (Thumb, 244 bytes, Stack size 96 bytes, tim.o(i.MX_TIM1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = MX_TIM1_Init &rArr; HAL_TIM_MspPostInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_ConfigBreakDeadTime
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Init
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_MspPostInit
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1f9]"></a>MX_TIM3_Init</STRONG> (Thumb, 142 bytes, Stack size 64 bytes, tim.o(i.MX_TIM3_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = MX_TIM3_Init &rArr; HAL_TIM_MspPostInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Init
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_MspPostInit
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1fa]"></a>MX_TIM4_Init</STRONG> (Thumb, 142 bytes, Stack size 64 bytes, tim.o(i.MX_TIM4_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = MX_TIM4_Init &rArr; HAL_TIM_MspPostInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Init
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_MspPostInit
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1fb]"></a>MX_TIM5_Init</STRONG> (Thumb, 174 bytes, Stack size 64 bytes, tim.o(i.MX_TIM5_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = MX_TIM5_Init &rArr; HAL_TIM_MspPostInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Init
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_MspPostInit
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1fc]"></a>MX_TIM8_Init</STRONG> (Thumb, 212 bytes, Stack size 96 bytes, tim.o(i.MX_TIM8_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = MX_TIM8_Init &rArr; HAL_TIM_MspPostInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_ConfigBreakDeadTime
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Init
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_MspPostInit
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1fd]"></a>MX_USART3_UART_Init</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, usart.o(i.MX_USART3_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = MX_USART3_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1fe]"></a>MX_USART6_UART_Init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, usart.o(i.MX_USART6_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = MX_USART6_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1ff]"></a>MX_USB_DEVICE_Init</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, usb_device.o(i.MX_USB_DEVICE_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = MX_USB_DEVICE_Init &rArr; USBD_Init &rArr; USBD_LL_Init &rArr; HAL_PCD_Init &rArr; HAL_PCD_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Start
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_RegisterClass
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Init
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CDC_RegisterInterface
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[20]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.MemManage_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[10e]"></a>New_PID_cail</STRONG> (Thumb, 406 bytes, Stack size 32 bytes, New_pid.o(i.New_PID_cail))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = New_PID_cail &rArr; activation_function &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;loop_fp32_constrain
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_max_limit
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;deal_err
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;activation_function
</UL>
<BR>[Called By]<UL><LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_control_loop
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOL_SpeedLoop
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fric_SpeedLoop
</UL>

<P><STRONG><a name="[221]"></a>New_PID_init</STRONG> (Thumb, 162 bytes, Stack size 0 bytes, New_pid.o(i.New_PID_init))
<BR><BR>[Called By]<UL><LI><a href="#[2ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_init
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SHOOT_InitArgument
</UL>

<P><STRONG><a name="[6a]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.OTG_FS_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = OTG_FS_IRQHandler &rArr; HAL_PCD_IRQHandler &rArr; PCD_EP_OutXfrComplete_int &rArr; HAL_PCD_SetupStageCallback &rArr; USBD_LL_SetupStage &rArr; USBD_StdDevReq &rArr; USBD_GetDescriptor &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[f6]"></a>PID_calc</STRONG> (Thumb, 346 bytes, Stack size 0 bytes, pid.o(i.PID_calc))
<BR><BR>[Called By]<UL><LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_temp_control
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_key_Contorl
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_Contorl
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Mouse_Move_Calculate
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Motor_Speed_PID_KEY
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Motor_Speed_PID
</UL>

<P><STRONG><a name="[e4]"></a>PID_init</STRONG> (Thumb, 88 bytes, Stack size 0 bytes, pid.o(i.PID_init))
<BR><BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_task
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Init
</UL>

<P><STRONG><a name="[20d]"></a>RAMP_float</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, ramp.o(i.RAMP_float))
<BR><BR>[Called By]<UL><LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_Rc_Ctrl
<LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_KeyPosiCtrl
</UL>

<P><STRONG><a name="[303]"></a>RC_Init</STRONG> (Thumb, 94 bytes, Stack size 12 bytes, bsp_rc.o(i.RC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = RC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[2c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remote_control_init
</UL>

<P><STRONG><a name="[291]"></a>RC_restart</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, bsp_rc.o(i.RC_restart))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RC_restart
</UL>
<BR>[Called By]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cali_gyro_hook
</UL>

<P><STRONG><a name="[292]"></a>RC_unable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, bsp_rc.o(i.RC_unable))
<BR><BR>[Called By]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cali_gyro_hook
</UL>

<P><STRONG><a name="[20c]"></a>REVOLVER_KeyPosiCtrl</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, shoot_task.o(i.REVOLVER_KeyPosiCtrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = REVOLVER_KeyPosiCtrl &rArr; REVOL_PositStuck &rArr; __hardfp_fabs
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
<LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RAMP_float
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOL_PositStuck
</UL>
<BR>[Called By]<UL><LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_Key_Ctrl
</UL>

<P><STRONG><a name="[20f]"></a>REVOLVER_KeySpeedCtrl</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, shoot_task.o(i.REVOLVER_KeySpeedCtrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = REVOLVER_KeySpeedCtrl &rArr; REVOL_SpeedStuck &rArr; __hardfp_fabs
</UL>
<BR>[Calls]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOL_SpeedStuck
</UL>
<BR>[Called By]<UL><LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_Key_Ctrl
</UL>

<P><STRONG><a name="[211]"></a>REVOLVER_Key_Ctrl</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, shoot_task.o(i.REVOLVER_Key_Ctrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = REVOLVER_Key_Ctrl &rArr; SHOOT_BUFF_Ctrl &rArr; REVOL_PositStuck &rArr; __hardfp_fabs
</UL>
<BR>[Calls]<UL><LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Shoot_Auto_Ctrl
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SHOOT_TRIPLE_Ctrl
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SHOOT_SINGLE_Ctrl
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SHOOT_NORMAL_Ctrl
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SHOOT_MIDF_HIGHTS_Ctrl
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SHOOT_Frq_Ctrl
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SHOOT_BUFF_Ctrl
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_KeySpeedCtrl
<LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_KeyPosiCtrl
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shoot_task
</UL>

<P><STRONG><a name="[219]"></a>REVOLVER_Rc_Ctrl</STRONG> (Thumb, 216 bytes, Stack size 32 bytes, shoot_task.o(i.REVOLVER_Rc_Ctrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = REVOLVER_Rc_Ctrl &rArr; Set_Fric_Speed &rArr; Friction_SpeedFIX &rArr; SpeedAdapt
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Reset_Fric
<LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RAMP_float
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Fric_Speed
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Revolver_Angle_Rest
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOL_SpeedStuck
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOL_PositStuck
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_Rc_Switch
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shoot_task
</UL>

<P><STRONG><a name="[21a]"></a>REVOLVER_Rc_Switch</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, shoot_task.o(i.REVOLVER_Rc_Switch))
<BR><BR>[Called By]<UL><LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_Rc_Ctrl
</UL>

<P><STRONG><a name="[304]"></a>REVOLVER_Rest</STRONG> (Thumb, 74 bytes, Stack size 0 bytes, shoot_task.o(i.REVOLVER_Rest))
<BR><BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shoot_task
</UL>

<P><STRONG><a name="[13b]"></a>REVOLVER_UpdateMotorAngle</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, shoot_task.o(i.REVOLVER_UpdateMotorAngle))
<BR><BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_RxFifo0MsgPendingCallback
</UL>

<P><STRONG><a name="[13c]"></a>REVOLVER_UpdateMotorSpeed</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, shoot_task.o(i.REVOLVER_UpdateMotorSpeed))
<BR><BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_RxFifo0MsgPendingCallback
</UL>

<P><STRONG><a name="[1e2]"></a>REVOLVER_open_off</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, shoot_task.o(i.REVOLVER_open_off))
<BR><BR>[Called By]<UL><LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_oncar
</UL>

<P><STRONG><a name="[20e]"></a>REVOL_PositStuck</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, shoot_task.o(i.REVOL_PositStuck))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = REVOL_PositStuck &rArr; __hardfp_fabs
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SHOOT_BUFF_Ctrl
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_Rc_Ctrl
<LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_KeyPosiCtrl
</UL>

<P><STRONG><a name="[21c]"></a>REVOL_PositionLoop</STRONG> (Thumb, 152 bytes, Stack size 16 bytes, shoot_task.o(i.REVOL_PositionLoop))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = REVOL_PositionLoop &rArr; REVOL_UpdateMotorAngleSum
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;constrain_float
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOL_UpdateMotorAngleSum
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shoot_task
</UL>

<P><STRONG><a name="[21e]"></a>REVOL_SpeedLoop</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, shoot_task.o(i.REVOL_SpeedLoop))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = REVOL_SpeedLoop &rArr; New_PID_cail &rArr; activation_function &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;New_PID_cail
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shoot_task
</UL>

<P><STRONG><a name="[210]"></a>REVOL_SpeedStuck</STRONG> (Thumb, 118 bytes, Stack size 16 bytes, shoot_task.o(i.REVOL_SpeedStuck))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = REVOL_SpeedStuck &rArr; __hardfp_fabs
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Shoot_Auto_Ctrl
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SHOOT_TRIPLE_Ctrl
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_Rc_Ctrl
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_KeySpeedCtrl
</UL>

<P><STRONG><a name="[21d]"></a>REVOL_UpdateMotorAngleSum</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, shoot_task.o(i.REVOL_UpdateMotorAngleSum))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = REVOL_UpdateMotorAngleSum
</UL>
<BR>[Called By]<UL><LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOL_PositionLoop
</UL>

<P><STRONG><a name="[1ad]"></a>RTC_EnterInitMode</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, stm32f4xx_hal_rtc.o(i.RTC_EnterInitMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RTC_EnterInitMode
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RTC_Init
</UL>

<P><STRONG><a name="[102]"></a>Reset_Fric</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, shoot_task.o(i.Reset_Fric))
<BR><BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_Mode
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_Rc_Ctrl
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fric_Key_Ctrl
</UL>

<P><STRONG><a name="[21b]"></a>Revolver_Angle_Rest</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, shoot_task.o(i.Revolver_Angle_Rest))
<BR><BR>[Called By]<UL><LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_Rc_Ctrl
</UL>

<P><STRONG><a name="[218]"></a>SHOOT_BUFF_Ctrl</STRONG> (Thumb, 292 bytes, Stack size 24 bytes, shoot_task.o(i.SHOOT_BUFF_Ctrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SHOOT_BUFF_Ctrl &rArr; REVOL_PositStuck &rArr; __hardfp_fabs
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SHOOT_SINGLE_Ctrl
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOL_PositStuck
</UL>
<BR>[Called By]<UL><LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_Key_Ctrl
</UL>

<P><STRONG><a name="[21f]"></a>SHOOT_CANbusCtrlMotor</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, shoot_task.o(i.SHOOT_CANbusCtrlMotor))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = SHOOT_CANbusCtrlMotor &rArr; CAN_cmd_gimbal_shoot &rArr; HAL_CAN_AddTxMessage
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN_cmd_gimbal_shoot
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN_cmd_Trigger
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;toe_is_error
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shoot_task
</UL>

<P><STRONG><a name="[213]"></a>SHOOT_Frq_Ctrl</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, shoot_task.o(i.SHOOT_Frq_Ctrl))
<BR><BR>[Called By]<UL><LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_Key_Ctrl
</UL>

<P><STRONG><a name="[220]"></a>SHOOT_InitArgument</STRONG> (Thumb, 322 bytes, Stack size 32 bytes, shoot_task.o(i.SHOOT_InitArgument))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SHOOT_InitArgument
</UL>
<BR>[Calls]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;New_PID_init
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shoot_task
</UL>

<P><STRONG><a name="[216]"></a>SHOOT_MIDF_HIGHTS_Ctrl</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, shoot_task.o(i.SHOOT_MIDF_HIGHTS_Ctrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = SHOOT_MIDF_HIGHTS_Ctrl &rArr; Fric_mode
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fric_mode
</UL>
<BR>[Called By]<UL><LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_Key_Ctrl
</UL>

<P><STRONG><a name="[212]"></a>SHOOT_NORMAL_Ctrl</STRONG> (Thumb, 308 bytes, Stack size 32 bytes, shoot_task.o(i.SHOOT_NORMAL_Ctrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SHOOT_NORMAL_Ctrl
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fric_open_off
<LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_shoot_heat0
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_shoot_cooling_limit
</UL>
<BR>[Called By]<UL><LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_Key_Ctrl
</UL>

<P><STRONG><a name="[214]"></a>SHOOT_SINGLE_Ctrl</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, shoot_task.o(i.SHOOT_SINGLE_Ctrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SHOOT_SINGLE_Ctrl
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
</UL>
<BR>[Called By]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SHOOT_BUFF_Ctrl
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_Key_Ctrl
</UL>

<P><STRONG><a name="[215]"></a>SHOOT_TRIPLE_Ctrl</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, shoot_task.o(i.SHOOT_TRIPLE_Ctrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SHOOT_TRIPLE_Ctrl &rArr; REVOL_SpeedStuck &rArr; __hardfp_fabs
</UL>
<BR>[Calls]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOL_SpeedStuck
</UL>
<BR>[Called By]<UL><LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_Key_Ctrl
</UL>

<P><STRONG><a name="[2bb]"></a>SPI1_DMA_enable</STRONG> (Thumb, 6204 bytes, Stack size 56 bytes, bsp_spi.o(i.SPI1_DMA_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = SPI1_DMA_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_cmd_spi_dma
</UL>

<P><STRONG><a name="[1d7]"></a>SPI1_DMA_init</STRONG> (Thumb, 242 bytes, Stack size 36 bytes, bsp_spi.o(i.SPI1_DMA_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SPI1_DMA_init
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_task
</UL>

<P><STRONG><a name="[10b]"></a>Set_Fric_Speed</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, shoot_task.o(i.Set_Fric_Speed))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Set_Fric_Speed &rArr; Friction_SpeedFIX &rArr; SpeedAdapt
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Friction_SpeedFIX
</UL>
<BR>[Called By]<UL><LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_Rc_Ctrl
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fric_Key_Ctrl
</UL>

<P><STRONG><a name="[217]"></a>Shoot_Auto_Ctrl</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, shoot_task.o(i.Shoot_Auto_Ctrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Shoot_Auto_Ctrl &rArr; REVOL_SpeedStuck &rArr; __hardfp_fabs
</UL>
<BR>[Calls]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOL_SpeedStuck
</UL>
<BR>[Called By]<UL><LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_Key_Ctrl
</UL>

<P><STRONG><a name="[115]"></a>SpeedAdapt</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, shoot_task.o(i.SpeedAdapt))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SpeedAdapt
</UL>
<BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Friction_SpeedFIX
</UL>

<P><STRONG><a name="[26]"></a>SysTick_Handler</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = SysTick_Handler &rArr; xPortSysTickHandler &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
<LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortSysTickHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[227]"></a>SystemClock_Config</STRONG> (Thumb, 166 bytes, Stack size 104 bytes, main.o(i.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[79]"></a>SystemInit</STRONG> (Thumb, 66 bytes, Stack size 0 bytes, system_stm32f4xx.o(i.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(.text)
</UL>
<P><STRONG><a name="[1b4]"></a>TIM_Base_SetConfig</STRONG> (Thumb, 160 bytes, Stack size 24 bytes, stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Init
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[1c4]"></a>TIM_CCxChannelCmd</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_CCxChannelCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_Start
</UL>

<P><STRONG><a name="[1b6]"></a>TIM_ETR_SetConfig</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_ETR_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[1bf]"></a>TIM_OC2_SetConfig</STRONG> (Thumb, 106 bytes, Stack size 20 bytes, stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_OC2_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
</UL>

<P><STRONG><a name="[116]"></a>Temp_Fix_25S</STRONG> (Thumb, 122 bytes, Stack size 0 bytes, shoot_task.o(i.Temp_Fix_25S))
<BR><BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Friction_SpeedFIX
</UL>

<P><STRONG><a name="[4c]"></a>USART1_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USART1_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>USART3_IRQHandler</STRONG> (Thumb, 182 bytes, Stack size 16 bytes, remote_control.o(i.USART3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = USART3_IRQHandler &rArr; sbus_to_rc
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;detect_hook
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sbus_to_rc
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[6e]"></a>USART6_IRQHandler</STRONG> (Thumb, 174 bytes, Stack size 16 bytes, referee_usart_task.o(i.USART6_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = USART6_IRQHandler &rArr; fifo_s_puts
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;detect_hook
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fifo_s_puts
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>USBD_CDC_GetDeviceQualifierDescriptor</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, usbd_cdc.o(i.USBD_CDC_GetDeviceQualifierDescriptor))
<BR>[Address Reference Count : 1]<UL><LI> usbd_cdc.o(.data)
</UL>
<P><STRONG><a name="[de]"></a>USBD_CDC_ReceivePacket</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, usbd_cdc.o(i.USBD_CDC_ReceivePacket))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = USBD_CDC_ReceivePacket &rArr; USBD_LL_PrepareReceive &rArr; HAL_PCD_EP_Receive &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_PrepareReceive
</UL>
<BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CDC_Receive_FS
</UL>

<P><STRONG><a name="[202]"></a>USBD_CDC_RegisterInterface</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, usbd_cdc.o(i.USBD_CDC_RegisterInterface))
<BR><BR>[Called By]<UL><LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USB_DEVICE_Init
</UL>

<P><STRONG><a name="[dc]"></a>USBD_CDC_SetRxBuffer</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, usbd_cdc.o(i.USBD_CDC_SetRxBuffer))
<BR><BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CDC_Receive_FS
<LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CDC_Init_FS
</UL>

<P><STRONG><a name="[db]"></a>USBD_CDC_SetTxBuffer</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, usbd_cdc.o(i.USBD_CDC_SetTxBuffer))
<BR><BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CDC_Transmit_FS
<LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CDC_Init_FS
</UL>

<P><STRONG><a name="[e0]"></a>USBD_CDC_TransmitPacket</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, usbd_cdc.o(i.USBD_CDC_TransmitPacket))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = USBD_CDC_TransmitPacket &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CDC_Transmit_FS
</UL>

<P><STRONG><a name="[250]"></a>USBD_ClrClassConfig</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, usbd_core.o(i.USBD_ClrClassConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USBD_ClrClassConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_SetConfig
</UL>

<P><STRONG><a name="[238]"></a>USBD_CtlContinueRx</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, usbd_ioreq.o(i.USBD_CtlContinueRx))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = USBD_CtlContinueRx &rArr; USBD_LL_PrepareReceive &rArr; HAL_PCD_EP_Receive &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_PrepareReceive
</UL>
<BR>[Called By]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_DataOutStage
</UL>

<P><STRONG><a name="[239]"></a>USBD_CtlContinueSendData</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, usbd_ioreq.o(i.USBD_CtlContinueSendData))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = USBD_CtlContinueSendData &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_DataInStage
</UL>

<P><STRONG><a name="[233]"></a>USBD_CtlError</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, usbd_ctlreq.o(i.USBD_CtlError))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = USBD_CtlError &rArr; USBD_LL_StallEP &rArr; HAL_PCD_EP_SetStall &rArr; USB_EP0_OutStart
</UL>
<BR>[Calls]<UL><LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_StallEP
</UL>
<BR>[Called By]<UL><LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_SetConfig
<LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_SetAddress
<LI><a href="#[23f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetStatus
<LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetDescriptor
<LI><a href="#[23d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetConfig
<LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_ClrFeature
<LI><a href="#[24b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdItfReq
<LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdEPReq
<LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdDevReq
<LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CDC_Setup
</UL>

<P><STRONG><a name="[235]"></a>USBD_CtlPrepareRx</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, usbd_ioreq.o(i.USBD_CtlPrepareRx))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = USBD_CtlPrepareRx &rArr; USBD_LL_PrepareReceive &rArr; HAL_PCD_EP_Receive &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_PrepareReceive
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CDC_Setup
</UL>

<P><STRONG><a name="[23b]"></a>USBD_CtlReceiveStatus</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, usbd_ioreq.o(i.USBD_CtlReceiveStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = USBD_CtlReceiveStatus &rArr; USBD_LL_PrepareReceive &rArr; HAL_PCD_EP_Receive &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_PrepareReceive
</UL>
<BR>[Called By]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_DataInStage
</UL>

<P><STRONG><a name="[234]"></a>USBD_CtlSendData</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, usbd_ioreq.o(i.USBD_CtlSendData))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = USBD_CtlSendData &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[23f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetStatus
<LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetDescriptor
<LI><a href="#[23d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetConfig
<LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdEPReq
<LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CDC_Setup
</UL>

<P><STRONG><a name="[237]"></a>USBD_CtlSendStatus</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, usbd_ioreq.o(i.USBD_CtlSendStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[252]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_SetFeature
<LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_SetConfig
<LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_SetAddress
<LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetDescriptor
<LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_ClrFeature
<LI><a href="#[24b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdItfReq
<LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdEPReq
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_DataOutStage
</UL>

<P><STRONG><a name="[14]"></a>USBD_FS_ConfigStrDescriptor</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, usbd_desc.o(i.USBD_FS_ConfigStrDescriptor))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USBD_FS_ConfigStrDescriptor &rArr; USBD_GetString
</UL>
<BR>[Calls]<UL><LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetString
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_desc.o(.data)
</UL>
<P><STRONG><a name="[f]"></a>USBD_FS_DeviceDescriptor</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, usbd_desc.o(i.USBD_FS_DeviceDescriptor))
<BR>[Address Reference Count : 1]<UL><LI> usbd_desc.o(.data)
</UL>
<P><STRONG><a name="[15]"></a>USBD_FS_InterfaceStrDescriptor</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, usbd_desc.o(i.USBD_FS_InterfaceStrDescriptor))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USBD_FS_InterfaceStrDescriptor &rArr; USBD_GetString
</UL>
<BR>[Calls]<UL><LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetString
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_desc.o(.data)
</UL>
<P><STRONG><a name="[10]"></a>USBD_FS_LangIDStrDescriptor</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, usbd_desc.o(i.USBD_FS_LangIDStrDescriptor))
<BR>[Address Reference Count : 1]<UL><LI> usbd_desc.o(.data)
</UL>
<P><STRONG><a name="[11]"></a>USBD_FS_ManufacturerStrDescriptor</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, usbd_desc.o(i.USBD_FS_ManufacturerStrDescriptor))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USBD_FS_ManufacturerStrDescriptor &rArr; USBD_GetString
</UL>
<BR>[Calls]<UL><LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetString
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_desc.o(.data)
</UL>
<P><STRONG><a name="[12]"></a>USBD_FS_ProductStrDescriptor</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, usbd_desc.o(i.USBD_FS_ProductStrDescriptor))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USBD_FS_ProductStrDescriptor &rArr; USBD_GetString
</UL>
<BR>[Calls]<UL><LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetString
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_desc.o(.data)
</UL>
<P><STRONG><a name="[13]"></a>USBD_FS_SerialStrDescriptor</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, usbd_desc.o(i.USBD_FS_SerialStrDescriptor))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = USBD_FS_SerialStrDescriptor &rArr; Get_SerialNum &rArr; IntToUnicode
</UL>
<BR>[Calls]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_SerialNum
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_desc.o(.data)
</UL>
<P><STRONG><a name="[23c]"></a>USBD_GetString</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, usbd_ctlreq.o(i.USBD_GetString))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USBD_GetString
</UL>
<BR>[Calls]<UL><LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetLen
</UL>
<BR>[Called By]<UL><LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_FS_ProductStrDescriptor
<LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_FS_ManufacturerStrDescriptor
<LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_FS_InterfaceStrDescriptor
<LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_FS_ConfigStrDescriptor
</UL>

<P><STRONG><a name="[243]"></a>USBD_Get_USB_Status</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, usbd_conf.o(i.USBD_Get_USB_Status))
<BR><BR>[Called By]<UL><LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_SetUSBAddress
<LI><a href="#[242]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_ClearStallEP
<LI><a href="#[24d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Start
<LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_StallEP
<LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Transmit
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_PrepareReceive
<LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_OpenEP
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_CloseEP
</UL>

<P><STRONG><a name="[200]"></a>USBD_Init</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, usbd_core.o(i.USBD_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = USBD_Init &rArr; USBD_LL_Init &rArr; HAL_PCD_Init &rArr; HAL_PCD_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USB_DEVICE_Init
</UL>

<P><STRONG><a name="[242]"></a>USBD_LL_ClearStallEP</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, usbd_conf.o(i.USBD_LL_ClearStallEP))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USBD_LL_ClearStallEP &rArr; HAL_PCD_EP_ClrStall
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_ClrStall
<LI><a href="#[243]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Get_USB_Status
</UL>
<BR>[Called By]<UL><LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdEPReq
</UL>

<P><STRONG><a name="[22e]"></a>USBD_LL_CloseEP</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, usbd_conf.o(i.USBD_LL_CloseEP))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = USBD_LL_CloseEP &rArr; HAL_PCD_EP_Close &rArr; USB_DeactivateEndpoint
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_Close
<LI><a href="#[243]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Get_USB_Status
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CDC_DeInit
</UL>

<P><STRONG><a name="[165]"></a>USBD_LL_DataInStage</STRONG> (Thumb, 214 bytes, Stack size 16 bytes, usbd_core.o(i.USBD_LL_DataInStage))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = USBD_LL_DataInStage &rArr; USBD_CtlReceiveStatus &rArr; USBD_LL_PrepareReceive &rArr; HAL_PCD_EP_Receive &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_StallEP
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlReceiveStatus
<LI><a href="#[239]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlContinueSendData
<LI><a href="#[244]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_RunTestMode
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_PrepareReceive
</UL>
<BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_DataInStageCallback
</UL>

<P><STRONG><a name="[167]"></a>USBD_LL_DataOutStage</STRONG> (Thumb, 134 bytes, Stack size 16 bytes, usbd_core.o(i.USBD_LL_DataOutStage))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = USBD_LL_DataOutStage &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_StallEP
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendStatus
<LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlContinueRx
</UL>
<BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_DataOutStageCallback
</UL>

<P><STRONG><a name="[163]"></a>USBD_LL_DevConnected</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, usbd_core.o(i.USBD_LL_DevConnected))
<BR><BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_ConnectCallback
</UL>

<P><STRONG><a name="[169]"></a>USBD_LL_DevDisconnected</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, usbd_core.o(i.USBD_LL_DevDisconnected))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USBD_LL_DevDisconnected
</UL>
<BR>[Called By]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_DisconnectCallback
</UL>

<P><STRONG><a name="[22d]"></a>USBD_LL_GetRxDataSize</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, usbd_conf.o(i.USBD_LL_GetRxDataSize))
<BR><BR>[Calls]<UL><LI><a href="#[245]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_GetRxCount
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CDC_DataOut
</UL>

<P><STRONG><a name="[241]"></a>USBD_LL_Init</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, usbd_conf.o(i.USBD_LL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = USBD_LL_Init &rArr; HAL_PCD_Init &rArr; HAL_PCD_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[247]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCDEx_SetTxFiFo
<LI><a href="#[246]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCDEx_SetRxFiFo
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Init
</UL>

<P><STRONG><a name="[253]"></a>USBD_LL_IsStallEP</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, usbd_conf.o(i.USBD_LL_IsStallEP))
<BR><BR>[Called By]<UL><LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdEPReq
</UL>

<P><STRONG><a name="[18e]"></a>USBD_LL_IsoINIncomplete</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, usbd_core.o(i.USBD_LL_IsoINIncomplete))
<BR><BR>[Called By]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_ISOINIncompleteCallback
</UL>

<P><STRONG><a name="[18f]"></a>USBD_LL_IsoOUTIncomplete</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, usbd_core.o(i.USBD_LL_IsoOUTIncomplete))
<BR><BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_ISOOUTIncompleteCallback
</UL>

<P><STRONG><a name="[230]"></a>USBD_LL_OpenEP</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, usbd_conf.o(i.USBD_LL_OpenEP))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = USBD_LL_OpenEP &rArr; HAL_PCD_EP_Open &rArr; USB_ActivateEndpoint
</UL>
<BR>[Calls]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_Open
<LI><a href="#[243]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Get_USB_Status
</UL>
<BR>[Called By]<UL><LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Reset
<LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CDC_Init
</UL>

<P><STRONG><a name="[232]"></a>USBD_LL_PrepareReceive</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, usbd_conf.o(i.USBD_LL_PrepareReceive))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = USBD_LL_PrepareReceive &rArr; HAL_PCD_EP_Receive &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_Receive
<LI><a href="#[243]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Get_USB_Status
</UL>
<BR>[Called By]<UL><LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlReceiveStatus
<LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlContinueRx
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_DataInStage
<LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlPrepareRx
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CDC_ReceivePacket
<LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CDC_Init
</UL>

<P><STRONG><a name="[199]"></a>USBD_LL_Reset</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, usbd_core.o(i.USBD_LL_Reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = USBD_LL_Reset &rArr; USBD_LL_OpenEP &rArr; HAL_PCD_EP_Open &rArr; USB_ActivateEndpoint
</UL>
<BR>[Calls]<UL><LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_OpenEP
</UL>
<BR>[Called By]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_ResetCallback
</UL>

<P><STRONG><a name="[19a]"></a>USBD_LL_Resume</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, usbd_core.o(i.USBD_LL_Resume))
<BR><BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_ResumeCallback
</UL>

<P><STRONG><a name="[19b]"></a>USBD_LL_SOF</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, usbd_core.o(i.USBD_LL_SOF))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USBD_LL_SOF
</UL>
<BR>[Called By]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_SOFCallback
</UL>

<P><STRONG><a name="[198]"></a>USBD_LL_SetSpeed</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usbd_core.o(i.USBD_LL_SetSpeed))
<BR><BR>[Called By]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_ResetCallback
</UL>

<P><STRONG><a name="[248]"></a>USBD_LL_SetUSBAddress</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, usbd_conf.o(i.USBD_LL_SetUSBAddress))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USBD_LL_SetUSBAddress &rArr; HAL_PCD_SetAddress
</UL>
<BR>[Calls]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_SetAddress
<LI><a href="#[243]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Get_USB_Status
</UL>
<BR>[Called By]<UL><LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_SetAddress
</UL>

<P><STRONG><a name="[19f]"></a>USBD_LL_SetupStage</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, usbd_core.o(i.USBD_LL_SetupStage))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = USBD_LL_SetupStage &rArr; USBD_StdDevReq &rArr; USBD_GetDescriptor &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[24b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdItfReq
<LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdEPReq
<LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdDevReq
<LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_ParseSetupRequest
<LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_StallEP
</UL>
<BR>[Called By]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_SetupStageCallback
</UL>

<P><STRONG><a name="[23a]"></a>USBD_LL_StallEP</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, usbd_conf.o(i.USBD_LL_StallEP))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = USBD_LL_StallEP &rArr; HAL_PCD_EP_SetStall &rArr; USB_EP0_OutStart
</UL>
<BR>[Calls]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_SetStall
<LI><a href="#[243]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Get_USB_Status
</UL>
<BR>[Called By]<UL><LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdEPReq
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_SetupStage
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_DataOutStage
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_DataInStage
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlError
</UL>

<P><STRONG><a name="[24d]"></a>USBD_LL_Start</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, usbd_conf.o(i.USBD_LL_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = USBD_LL_Start &rArr; HAL_PCD_Start &rArr; USB_DevConnect &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Start
<LI><a href="#[243]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Get_USB_Status
</UL>
<BR>[Called By]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Start
</UL>

<P><STRONG><a name="[1a3]"></a>USBD_LL_Suspend</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, usbd_core.o(i.USBD_LL_Suspend))
<BR><BR>[Called By]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_SuspendCallback
</UL>

<P><STRONG><a name="[22c]"></a>USBD_LL_Transmit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, usbd_conf.o(i.USBD_LL_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_Transmit
<LI><a href="#[243]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_Get_USB_Status
</UL>
<BR>[Called By]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendStatus
<LI><a href="#[239]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlContinueSendData
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendData
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CDC_TransmitPacket
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CDC_DataIn
</UL>

<P><STRONG><a name="[249]"></a>USBD_ParseSetupRequest</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, usbd_ctlreq.o(i.USBD_ParseSetupRequest))
<BR><BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_SetupStage
</UL>

<P><STRONG><a name="[201]"></a>USBD_RegisterClass</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, usbd_core.o(i.USBD_RegisterClass))
<BR><BR>[Called By]<UL><LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USB_DEVICE_Init
</UL>

<P><STRONG><a name="[244]"></a>USBD_RunTestMode</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, usbd_core.o(i.USBD_RunTestMode))
<BR><BR>[Called By]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_DataInStage
</UL>

<P><STRONG><a name="[251]"></a>USBD_SetClassConfig</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, usbd_core.o(i.USBD_SetClassConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USBD_SetClassConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_SetConfig
</UL>

<P><STRONG><a name="[203]"></a>USBD_Start</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, usbd_core.o(i.USBD_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = USBD_Start &rArr; USBD_LL_Start &rArr; HAL_PCD_Start &rArr; USB_DevConnect &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[24d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USB_DEVICE_Init
</UL>

<P><STRONG><a name="[24a]"></a>USBD_StdDevReq</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, usbd_ctlreq.o(i.USBD_StdDevReq))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = USBD_StdDevReq &rArr; USBD_GetDescriptor &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[252]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_SetFeature
<LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_SetConfig
<LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_SetAddress
<LI><a href="#[23f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetStatus
<LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetDescriptor
<LI><a href="#[23d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetConfig
<LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_ClrFeature
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlError
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_SetupStage
</UL>

<P><STRONG><a name="[24c]"></a>USBD_StdEPReq</STRONG> (Thumb, 484 bytes, Stack size 24 bytes, usbd_ctlreq.o(i.USBD_StdEPReq))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = USBD_StdEPReq &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[253]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_IsStallEP
<LI><a href="#[242]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_ClearStallEP
<LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_StallEP
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendStatus
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendData
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlError
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_SetupStage
</UL>

<P><STRONG><a name="[24b]"></a>USBD_StdItfReq</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, usbd_ctlreq.o(i.USBD_StdItfReq))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = USBD_StdItfReq &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendStatus
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlError
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_SetupStage
</UL>

<P><STRONG><a name="[16f]"></a>USB_ActivateEndpoint</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, stm32f4xx_ll_usb.o(i.USB_ActivateEndpoint))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USB_ActivateEndpoint
</UL>
<BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_Open
</UL>

<P><STRONG><a name="[185]"></a>USB_ActivateSetup</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, stm32f4xx_ll_usb.o(i.USB_ActivateSetup))
<BR><BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[194]"></a>USB_CoreInit</STRONG> (Thumb, 122 bytes, Stack size 24 bytes, stm32f4xx_ll_usb.o(i.USB_CoreInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USB_CoreInit
</UL>
<BR>[Calls]<UL><LI><a href="#[254]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_CoreReset
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Init
</UL>

<P><STRONG><a name="[16b]"></a>USB_DeactivateEndpoint</STRONG> (Thumb, 122 bytes, Stack size 16 bytes, stm32f4xx_ll_usb.o(i.USB_DeactivateEndpoint))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USB_DeactivateEndpoint
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_Close
</UL>

<P><STRONG><a name="[1a1]"></a>USB_DevConnect</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, stm32f4xx_ll_usb.o(i.USB_DevConnect))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USB_DevConnect &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Called By]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Start
</UL>

<P><STRONG><a name="[197]"></a>USB_DevDisconnect</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, stm32f4xx_ll_usb.o(i.USB_DevDisconnect))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USB_DevDisconnect &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Init
</UL>

<P><STRONG><a name="[196]"></a>USB_DevInit</STRONG> (Thumb, 370 bytes, Stack size 36 bytes, stm32f4xx_ll_usb.o(i.USB_DevInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = USB_DevInit
</UL>
<BR>[Calls]<UL><LI><a href="#[255]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_SetDevSpeed
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_FlushTxFifo
<LI><a href="#[256]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_FlushRxFifo
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Init
</UL>

<P><STRONG><a name="[192]"></a>USB_DisableGlobalInt</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_ll_usb.o(i.USB_DisableGlobalInt))
<BR><BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Init
</UL>

<P><STRONG><a name="[172]"></a>USB_EP0StartXfer</STRONG> (Thumb, 272 bytes, Stack size 16 bytes, stm32f4xx_ll_usb.o(i.USB_EP0StartXfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USB_EP0StartXfer
</UL>
<BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_Transmit
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_Receive
</UL>

<P><STRONG><a name="[175]"></a>USB_EP0_OutStart</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, stm32f4xx_ll_usb.o(i.USB_EP0_OutStart))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USB_EP0_OutStart
</UL>
<BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_SetStall
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PCD_EP_OutXfrComplete_int
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PCD_EP_OutSetupPacket_int
</UL>

<P><STRONG><a name="[16d]"></a>USB_EPClearStall</STRONG> (Thumb, 88 bytes, Stack size 0 bytes, stm32f4xx_ll_usb.o(i.USB_EPClearStall))
<BR><BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_ClrStall
</UL>

<P><STRONG><a name="[174]"></a>USB_EPSetStall</STRONG> (Thumb, 88 bytes, Stack size 0 bytes, stm32f4xx_ll_usb.o(i.USB_EPSetStall))
<BR><BR>[Called By]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_SetStall
</UL>

<P><STRONG><a name="[171]"></a>USB_EPStartXfer</STRONG> (Thumb, 496 bytes, Stack size 24 bytes, stm32f4xx_ll_usb.o(i.USB_EPStartXfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_WritePacket
</UL>
<BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_Transmit
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_EP_Receive
</UL>

<P><STRONG><a name="[1a2]"></a>USB_EnableGlobalInt</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_ll_usb.o(i.USB_EnableGlobalInt))
<BR><BR>[Called By]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Start
</UL>

<P><STRONG><a name="[256]"></a>USB_FlushRxFifo</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, stm32f4xx_ll_usb.o(i.USB_FlushRxFifo))
<BR><BR>[Called By]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DevInit
</UL>

<P><STRONG><a name="[183]"></a>USB_FlushTxFifo</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f4xx_ll_usb.o(i.USB_FlushTxFifo))
<BR><BR>[Called By]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DevInit
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[186]"></a>USB_GetDevSpeed</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f4xx_ll_usb.o(i.USB_GetDevSpeed))
<BR><BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[178]"></a>USB_GetMode</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_ll_usb.o(i.USB_GetMode))
<BR><BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[17e]"></a>USB_ReadDevAllInEpInterrupt</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f4xx_ll_usb.o(i.USB_ReadDevAllInEpInterrupt))
<BR><BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[17a]"></a>USB_ReadDevAllOutEpInterrupt</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f4xx_ll_usb.o(i.USB_ReadDevAllOutEpInterrupt))
<BR><BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[17f]"></a>USB_ReadDevInEPInterrupt</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, stm32f4xx_ll_usb.o(i.USB_ReadDevInEPInterrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USB_ReadDevInEPInterrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[17b]"></a>USB_ReadDevOutEPInterrupt</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32f4xx_ll_usb.o(i.USB_ReadDevOutEPInterrupt))
<BR><BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[179]"></a>USB_ReadInterrupts</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_ll_usb.o(i.USB_ReadInterrupts))
<BR><BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[18a]"></a>USB_ReadPacket</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, stm32f4xx_ll_usb.o(i.USB_ReadPacket))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USB_ReadPacket
</UL>
<BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[195]"></a>USB_SetCurrentMode</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, stm32f4xx_ll_usb.o(i.USB_SetCurrentMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USB_SetCurrentMode &rArr; HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>
<BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_Init
</UL>

<P><STRONG><a name="[19d]"></a>USB_SetDevAddress</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, stm32f4xx_ll_usb.o(i.USB_SetDevAddress))
<BR><BR>[Called By]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_SetAddress
</UL>

<P><STRONG><a name="[255]"></a>USB_SetDevSpeed</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f4xx_ll_usb.o(i.USB_SetDevSpeed))
<BR><BR>[Called By]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_DevInit
</UL>

<P><STRONG><a name="[188]"></a>USB_SetTurnaroundTime</STRONG> (Thumb, 170 bytes, Stack size 0 bytes, stm32f4xx_ll_usb.o(i.USB_SetTurnaroundTime))
<BR><BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[207]"></a>USB_WritePacket</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, stm32f4xx_ll_usb.o(i.USB_WritePacket))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USB_WritePacket
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_EPStartXfer
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PCD_WriteEmptyTxFifo
</UL>

<P><STRONG><a name="[22]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.UsageFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f407xx.o(RESET)
</UL>
<P><STRONG><a name="[119]"></a>Vision_Error_Angle_PITCH</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, usb_task.o(i.Vision_Error_Angle_PITCH))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = Vision_Error_Angle_PITCH &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GIMBAL_AUTO_Mode_Ctrl
</UL>

<P><STRONG><a name="[118]"></a>Vision_Error_Angle_YAW</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, usb_task.o(i.Vision_Error_Angle_YAW))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = Vision_Error_Angle_YAW &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GIMBAL_AUTO_Mode_Ctrl
</UL>

<P><STRONG><a name="[1e0]"></a>Vision_Get_Fucking_FLAG</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usb_task.o(i.Vision_Get_Fucking_FLAG))
<BR><BR>[Called By]<UL><LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_update_g_Dynamic_3
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_update_g_Dynamic_1
<LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_update_g_Dynamic_0
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_oncar
</UL>

<P><STRONG><a name="[11a]"></a>Vision_If_UpDate</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usb_task.o(i.Vision_If_UpDate))
<BR><BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GIMBAL_AUTO_Mode_Ctrl
</UL>

<P><STRONG><a name="[30f]"></a>Vision_Limit</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, usb_task.o(i.Vision_Limit))
<BR><BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_read_data
</UL>

<P><STRONG><a name="[265]"></a>__ARM_fpclassify</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, fpclassify.o(i.__ARM_fpclassify))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __ARM_fpclassify
</UL>
<BR>[Called By]<UL><LI><a href="#[261]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
</UL>

<P><STRONG><a name="[258]"></a>__ARM_fpclassifyf</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, fpclassifyf.o(i.__ARM_fpclassifyf))
<BR><BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asinf
</UL>

<P><STRONG><a name="[a5]"></a>__hardfp_asinf</STRONG> (Thumb, 258 bytes, Stack size 16 bytes, asinf.o(i.__hardfp_asinf))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __hardfp_asinf &rArr; sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[257]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrtf
<LI><a href="#[25b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[259]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_underflow
<LI><a href="#[25c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_invalid
<LI><a href="#[25a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan
<LI><a href="#[258]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassifyf
</UL>
<BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_asinf
</UL>

<P><STRONG><a name="[a7]"></a>__hardfp_atan2f</STRONG> (Thumb, 594 bytes, Stack size 32 bytes, atan2f.o(i.__hardfp_atan2f))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __hardfp_atan2f
</UL>
<BR>[Calls]<UL><LI><a href="#[25b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[259]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_underflow
<LI><a href="#[25d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan2
<LI><a href="#[258]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassifyf
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_atan2f
</UL>

<P><STRONG><a name="[b9]"></a>__hardfp_cos</STRONG> (Thumb, 180 bytes, Stack size 32 bytes, cos.o(i.__hardfp_cos))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[25b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[261]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[260]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[25e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[25f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[262]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_Contorl
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Angle_error
</UL>

<P><STRONG><a name="[ef]"></a>__hardfp_fabs</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, fabs.o(i.__hardfp_fabs))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;girt_powerLimit
<LI><a href="#[2a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_init_control
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_behavour_set
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_key_Contorl
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_Contorl
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Motor_Speed_PID
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Keyboard_Move_Calculate
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOL_SpeedStuck
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOL_PositStuck
</UL>

<P><STRONG><a name="[ba]"></a>__hardfp_sin</STRONG> (Thumb, 180 bytes, Stack size 32 bytes, sin.o(i.__hardfp_sin))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[25b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[261]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[260]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[25e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[25f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[262]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>
<BR>[Called By]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_Contorl
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Angle_error
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;activation_function
</UL>

<P><STRONG><a name="[ec]"></a>__hardfp_sqrt</STRONG> (Thumb, 122 bytes, Stack size 32 bytes, sqrt_full.o(i.__hardfp_sqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = __hardfp_sqrt &rArr; _dsqrt &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[25b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;girt_powerLimit
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Key_MoveRamp
</UL>

<P><STRONG><a name="[b0]"></a>__hardfp_sqrtf</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, sqrtf_full.o(i.__hardfp_sqrtf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __hardfp_sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[25b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_invSqrt
</UL>

<P><STRONG><a name="[260]"></a>__ieee754_rem_pio2</STRONG> (Thumb, 938 bytes, Stack size 120 bytes, rred.o(i.__ieee754_rem_pio2))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[263]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
</UL>

<P><STRONG><a name="[262]"></a>__kernel_cos</STRONG> (Thumb, 322 bytes, Stack size 64 bytes, cos_i.o(i.__kernel_cos))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = __kernel_cos &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[264]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
</UL>

<P><STRONG><a name="[264]"></a>__kernel_poly</STRONG> (Thumb, 248 bytes, Stack size 24 bytes, poly.o(i.__kernel_poly))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[261]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[262]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>

<P><STRONG><a name="[261]"></a>__kernel_sin</STRONG> (Thumb, 280 bytes, Stack size 72 bytes, sin_i.o(i.__kernel_sin))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = __kernel_sin &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[264]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[266]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
</UL>

<P><STRONG><a name="[25f]"></a>__mathlib_dbl_infnan</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_infnan))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __mathlib_dbl_infnan &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
</UL>

<P><STRONG><a name="[25e]"></a>__mathlib_dbl_invalid</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_invalid))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = __mathlib_dbl_invalid &rArr; __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
</UL>

<P><STRONG><a name="[266]"></a>__mathlib_dbl_underflow</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_underflow))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __mathlib_dbl_underflow &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[261]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
</UL>

<P><STRONG><a name="[25a]"></a>__mathlib_flt_infnan</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_infnan))
<BR><BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asinf
</UL>

<P><STRONG><a name="[25d]"></a>__mathlib_flt_infnan2</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_infnan2))
<BR><BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>

<P><STRONG><a name="[25c]"></a>__mathlib_flt_invalid</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_invalid))
<BR><BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asinf
</UL>

<P><STRONG><a name="[259]"></a>__mathlib_flt_underflow</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_underflow))
<BR><BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asinf
</UL>

<P><STRONG><a name="[33c]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[33d]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[33e]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[25b]"></a>__set_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__set_errno))
<BR><BR>[Called By]<UL><LI><a href="#[257]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrtf
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrtf
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asinf
</UL>

<P><STRONG><a name="[267]"></a>_ui_init_g_Dynamic_0</STRONG> (Thumb, 530 bytes, Stack size 16 bytes, ui_g_Dynamic_0.o(i._ui_init_g_Dynamic_0))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _ui_init_g_Dynamic_0 &rArr; usart6_tx_dma_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[269]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart6_tx_dma_enable
<LI><a href="#[268]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_proc_5_frame
</UL>
<BR>[Called By]<UL><LI><a href="#[306]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_init
</UL>

<P><STRONG><a name="[26a]"></a>_ui_init_g_Dynamic_1</STRONG> (Thumb, 700 bytes, Stack size 24 bytes, ui_g_Dynamic_1.o(i._ui_init_g_Dynamic_1))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _ui_init_g_Dynamic_1 &rArr; ui_proc_7_frame &rArr; calc_crc8
</UL>
<BR>[Calls]<UL><LI><a href="#[26b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_proc_7_frame
<LI><a href="#[269]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart6_tx_dma_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[306]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_init
</UL>

<P><STRONG><a name="[26c]"></a>_ui_init_g_Dynamic_2</STRONG> (Thumb, 670 bytes, Stack size 16 bytes, ui_g_Dynamic_2.o(i._ui_init_g_Dynamic_2))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _ui_init_g_Dynamic_2 &rArr; ui_proc_7_frame &rArr; calc_crc8
</UL>
<BR>[Calls]<UL><LI><a href="#[26b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_proc_7_frame
<LI><a href="#[269]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart6_tx_dma_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[306]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_init
</UL>

<P><STRONG><a name="[26d]"></a>_ui_init_g_Dynamic_3</STRONG> (Thumb, 424 bytes, Stack size 16 bytes, ui_g_Dynamic_3.o(i._ui_init_g_Dynamic_3))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _ui_init_g_Dynamic_3 &rArr; ui_proc_7_frame &rArr; calc_crc8
</UL>
<BR>[Calls]<UL><LI><a href="#[26b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_proc_7_frame
<LI><a href="#[269]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart6_tx_dma_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[306]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_init
</UL>

<P><STRONG><a name="[26e]"></a>_ui_init_g_Ungroup_0</STRONG> (Thumb, 142 bytes, Stack size 8 bytes, ui_g_Ungroup_0.o(i._ui_init_g_Ungroup_0))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _ui_init_g_Ungroup_0 &rArr; ui_proc_string_frame &rArr; calc_crc8
</UL>
<BR>[Calls]<UL><LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_proc_string_frame
<LI><a href="#[269]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart6_tx_dma_enable
<LI><a href="#[26f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[306]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_init
</UL>

<P><STRONG><a name="[271]"></a>_ui_init_g_Ungroup_1</STRONG> (Thumb, 142 bytes, Stack size 8 bytes, ui_g_Ungroup_1.o(i._ui_init_g_Ungroup_1))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _ui_init_g_Ungroup_1 &rArr; ui_proc_string_frame &rArr; calc_crc8
</UL>
<BR>[Calls]<UL><LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_proc_string_frame
<LI><a href="#[269]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart6_tx_dma_enable
<LI><a href="#[26f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[306]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_init
</UL>

<P><STRONG><a name="[272]"></a>_ui_init_g_Ungroup_2</STRONG> (Thumb, 144 bytes, Stack size 8 bytes, ui_g_Ungroup_2.o(i._ui_init_g_Ungroup_2))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _ui_init_g_Ungroup_2 &rArr; ui_proc_string_frame &rArr; calc_crc8
</UL>
<BR>[Calls]<UL><LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_proc_string_frame
<LI><a href="#[269]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart6_tx_dma_enable
<LI><a href="#[26f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[306]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_init
</UL>

<P><STRONG><a name="[273]"></a>_ui_init_g_Ungroup_3</STRONG> (Thumb, 140 bytes, Stack size 8 bytes, ui_g_Ungroup_3.o(i._ui_init_g_Ungroup_3))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _ui_init_g_Ungroup_3 &rArr; ui_proc_string_frame &rArr; calc_crc8
</UL>
<BR>[Calls]<UL><LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_proc_string_frame
<LI><a href="#[269]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart6_tx_dma_enable
<LI><a href="#[26f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[306]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_init
</UL>

<P><STRONG><a name="[274]"></a>_ui_init_g_Ungroup_4</STRONG> (Thumb, 138 bytes, Stack size 8 bytes, ui_g_Ungroup_4.o(i._ui_init_g_Ungroup_4))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _ui_init_g_Ungroup_4 &rArr; ui_proc_string_frame &rArr; calc_crc8
</UL>
<BR>[Calls]<UL><LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_proc_string_frame
<LI><a href="#[269]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart6_tx_dma_enable
<LI><a href="#[26f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[306]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_init
</UL>

<P><STRONG><a name="[275]"></a>_ui_init_g_Ungroup_5</STRONG> (Thumb, 180 bytes, Stack size 16 bytes, ui_g_Ungroup_5.o(i._ui_init_g_Ungroup_5))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _ui_init_g_Ungroup_5 &rArr; usart6_tx_dma_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[269]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart6_tx_dma_enable
<LI><a href="#[268]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_proc_5_frame
</UL>
<BR>[Called By]<UL><LI><a href="#[306]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_init
</UL>

<P><STRONG><a name="[18]"></a>_ui_update_g_Dynamic_0</STRONG> (Thumb, 1178 bytes, Stack size 56 bytes, ui_g_Dynamic_0.o(i._ui_update_g_Dynamic_0))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = _ui_update_g_Dynamic_0 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[269]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart6_tx_dma_enable
<LI><a href="#[268]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_proc_5_frame
<LI><a href="#[276]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_vision_flag
<LI><a href="#[27b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_enemy_hp7
<LI><a href="#[27a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_enemy_hp4
<LI><a href="#[279]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_enemy_hp3
<LI><a href="#[278]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_enemy_hp2
<LI><a href="#[277]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_enemy_hp1
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vision_Get_Fucking_FLAG
</UL>
<BR>[Address Reference Count : 1]<UL><LI> UI_task.o(.data)
</UL>
<P><STRONG><a name="[17]"></a>_ui_update_g_Dynamic_1</STRONG> (Thumb, 680 bytes, Stack size 24 bytes, ui_g_Dynamic_1.o(i._ui_update_g_Dynamic_1))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _ui_update_g_Dynamic_1 &rArr; ui_proc_7_frame &rArr; calc_crc8
</UL>
<BR>[Calls]<UL><LI><a href="#[26b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_proc_7_frame
<LI><a href="#[269]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart6_tx_dma_enable
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vision_Get_Fucking_FLAG
</UL>
<BR>[Address Reference Count : 1]<UL><LI> UI_task.o(.data)
</UL>
<P><STRONG><a name="[16]"></a>_ui_update_g_Dynamic_2</STRONG> (Thumb, 560 bytes, Stack size 16 bytes, ui_g_Dynamic_2.o(i._ui_update_g_Dynamic_2))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _ui_update_g_Dynamic_2 &rArr; ui_proc_7_frame &rArr; calc_crc8
</UL>
<BR>[Calls]<UL><LI><a href="#[27c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_game_progress
<LI><a href="#[27d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_bullet_remaining
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fric_open_off
<LI><a href="#[26b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_proc_7_frame
<LI><a href="#[269]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart6_tx_dma_enable
</UL>
<BR>[Address Reference Count : 1]<UL><LI> UI_task.o(.data)
</UL>
<P><STRONG><a name="[19]"></a>_ui_update_g_Dynamic_3</STRONG> (Thumb, 226 bytes, Stack size 8 bytes, ui_g_Dynamic_3.o(i._ui_update_g_Dynamic_3))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _ui_update_g_Dynamic_3 &rArr; ui_proc_7_frame &rArr; calc_crc8
</UL>
<BR>[Calls]<UL><LI><a href="#[26b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_proc_7_frame
<LI><a href="#[269]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart6_tx_dma_enable
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vision_Get_Fucking_FLAG
</UL>
<BR>[Address Reference Count : 1]<UL><LI> UI_task.o(.data)
</UL>
<P><STRONG><a name="[2c6]"></a>aRGB_led_show</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, bsp_led.o(i.aRGB_led_show))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = aRGB_led_show
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_RGB_flow_task
</UL>

<P><STRONG><a name="[284]"></a>beepUpdate</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, music_task.o(i.beepUpdate))
<BR><BR>[Calls]<UL><LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buzzer_on
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;music_task
</UL>

<P><STRONG><a name="[c8]"></a>bmi088_accel_init</STRONG> (Thumb, 278 bytes, Stack size 24 bytes, BMI088driver.o(i.bmi088_accel_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = bmi088_accel_init &rArr; BMI088_write_single_reg &rArr; BMI088_read_write_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_write_single_reg
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_read_write_byte
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_delay_us
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_delay_ms
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_ACCEL_NS_L
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_ACCEL_NS_H
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_init
</UL>

<P><STRONG><a name="[c7]"></a>bmi088_accel_self_test</STRONG> (Thumb, 684 bytes, Stack size 48 bytes, BMI088driver.o(i.bmi088_accel_self_test))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = bmi088_accel_self_test &rArr; BMI088_read_muli_reg &rArr; BMI088_read_write_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_write_single_reg
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_read_muli_reg
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_read_write_byte
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_delay_us
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_delay_ms
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_ACCEL_NS_L
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_ACCEL_NS_H
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_init
</UL>

<P><STRONG><a name="[ca]"></a>bmi088_gyro_init</STRONG> (Thumb, 214 bytes, Stack size 24 bytes, BMI088driver.o(i.bmi088_gyro_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = bmi088_gyro_init &rArr; BMI088_write_single_reg &rArr; BMI088_read_write_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_write_single_reg
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_read_single_reg
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_delay_us
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_delay_ms
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_GYRO_NS_L
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_GYRO_NS_H
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_init
</UL>

<P><STRONG><a name="[c9]"></a>bmi088_gyro_self_test</STRONG> (Thumb, 202 bytes, Stack size 16 bytes, BMI088driver.o(i.bmi088_gyro_self_test))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = bmi088_gyro_self_test &rArr; BMI088_write_single_reg &rArr; BMI088_read_write_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_write_single_reg
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_read_single_reg
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_delay_us
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_delay_ms
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_GYRO_NS_L
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_GYRO_NS_H
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_init
</UL>

<P><STRONG><a name="[209]"></a>buzzer_off</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, bsp_buzzer.o(i.buzzer_off))
<BR><BR>[Called By]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cali_gyro_hook
<LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cali_gimbal_hook
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RC_cmd_to_calibrate
<LI><a href="#[285]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buzzer_warn_error
</UL>

<P><STRONG><a name="[20b]"></a>buzzer_on</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, bsp_buzzer.o(i.buzzer_on))
<BR><BR>[Called By]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cali_gyro_hook
<LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cali_gimbal_hook
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RC_cmd_to_calibrate
<LI><a href="#[284]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;beepUpdate
<LI><a href="#[285]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buzzer_warn_error
</UL>

<P><STRONG><a name="[309]"></a>calc_crc16</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, ui_interface.o(i.calc_crc16))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = calc_crc16
</UL>
<BR>[Called By]<UL><LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_proc_string_frame
<LI><a href="#[26b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_proc_7_frame
<LI><a href="#[268]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_proc_5_frame
</UL>

<P><STRONG><a name="[307]"></a>calc_crc8</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, ui_interface.o(i.calc_crc8))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = calc_crc8
</UL>
<BR>[Called By]<UL><LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_proc_string_frame
<LI><a href="#[26b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_proc_7_frame
<LI><a href="#[268]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_proc_5_frame
</UL>

<P><STRONG><a name="[293]"></a>cali_param_init</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, calibrate_task.o(i.cali_param_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = cali_param_init &rArr; cali_data_read
</UL>
<BR>[Calls]<UL><LI><a href="#[288]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cali_data_read
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[80]"></a>calibrate_task</STRONG> (Thumb, 124 bytes, Stack size 0 bytes, calibrate_task.o(i.calibrate_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = calibrate_task &rArr; cali_data_write &rArr; flash_erase_address &rArr; HAL_FLASHEx_Erase &rArr; FLASH_WaitForLastOperation
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
<LI><a href="#[294]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_remote_control_point
<LI><a href="#[28a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cali_data_write
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RC_cmd_to_calibrate
</UL>
<BR>[Address Reference Count : 1]<UL><LI> freertos.o(.constdata)
</UL>
<P><STRONG><a name="[295]"></a>can_filter_init</STRONG> (Thumb, 80 bytes, Stack size 48 bytes, bsp_can.o(i.can_filter_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = can_filter_init &rArr; HAL_CAN_Start
</UL>
<BR>[Calls]<UL><LI><a href="#[296]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_ConfigFilter
<LI><a href="#[297]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_ActivateNotification
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_Start
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e6]"></a>chassis_feedback_update</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, chassis_task.o(i.chassis_feedback_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = chassis_feedback_update
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chassis_task
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Init
</UL>

<P><STRONG><a name="[fe]"></a>chassis_follow_gimbal_yaw_control</STRONG> (Thumb, 154 bytes, Stack size 8 bytes, chassis_behaviour.o(i.chassis_follow_gimbal_yaw_control))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = chassis_follow_gimbal_yaw_control
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_sin_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_key_Contorl
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_Contorl
</UL>

<P><STRONG><a name="[fb]"></a>chassis_move</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gimbal_task.o(i.chassis_move))
<BR><BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Omni_Move_Calculate
</UL>

<P><STRONG><a name="[81]"></a>chassis_task</STRONG> (Thumb, 126 bytes, Stack size 0 bytes, chassis_task.o(i.chassis_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = chassis_task &rArr; Chassis_Set_Contorl &rArr; Angle_error &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[298]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;girt_powerLimit
<LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;power_data_sent
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chassis_feedback_update
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_switch
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_key_Contorl
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_Mode
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_Contorl
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Rc_Control
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Omni_Move_Calculate
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Motor_Speed_PID_KEY
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Motor_Speed_PID
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_MotorOutput
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Key_Ctrl
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Init
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CHASSIS_CANSend
</UL>
<BR>[Address Reference Count : 1]<UL><LI> freertos.o(.constdata)
</UL>
<P><STRONG><a name="[28f]"></a>cmd_cali_gimbal_hook</STRONG> (Thumb, 226 bytes, Stack size 48 bytes, gimbal_task.o(i.cmd_cali_gimbal_hook))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = cmd_cali_gimbal_hook &rArr; calc_gimbal_cali
</UL>
<BR>[Calls]<UL><LI><a href="#[286]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_gimbal_cali
</UL>
<BR>[Called By]<UL><LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cali_gimbal_hook
</UL>

<P><STRONG><a name="[ed]"></a>constrain_float</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, user_lib.o(i.constrain_float))
<BR><BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Mouse_Move_Calculate
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Motor_Key_PID
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Keyboard_Move_Calculate
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Key_MoveRamp
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOL_PositionLoop
</UL>

<P><STRONG><a name="[2c7]"></a>delay_init</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, bsp_delay.o(i.delay_init))
<BR><BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c3]"></a>delay_us</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, bsp_delay.o(i.delay_us))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_delay_us
</UL>

<P><STRONG><a name="[134]"></a>detect_hook</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, detect_task.o(i.detect_hook))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = detect_hook
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_RxFifo0MsgPendingCallback
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_Callback
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_IRQHandler
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART6_IRQHandler
</UL>

<P><STRONG><a name="[82]"></a>detect_task</STRONG> (Thumb, 248 bytes, Stack size 0 bytes, detect_task.o(i.detect_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = detect_task &rArr; detect_init
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
<LI><a href="#[298]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[29b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;detect_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> freertos.o(.constdata)
</UL>
<P><STRONG><a name="[263]"></a>fabs</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, fabs.o(i.fabs))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[260]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
</UL>

<P><STRONG><a name="[2fd]"></a>fifo_s_get</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, fifo.o(i.fifo_s_get))
<BR><BR>[Called By]<UL><LI><a href="#[2fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;referee_unpack_fifo_data
</UL>

<P><STRONG><a name="[301]"></a>fifo_s_init</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, fifo.o(i.fifo_s_init))
<BR><BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;referee_usart_task
</UL>

<P><STRONG><a name="[22b]"></a>fifo_s_puts</STRONG> (Thumb, 132 bytes, Stack size 32 bytes, fifo.o(i.fifo_s_puts))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = fifo_s_puts
</UL>
<BR>[Calls]<UL><LI><a href="#[28b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART6_IRQHandler
</UL>

<P><STRONG><a name="[300]"></a>fifo_s_used</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, fifo.o(i.fifo_s_used))
<BR><BR>[Called By]<UL><LI><a href="#[2fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;referee_unpack_fifo_data
</UL>

<P><STRONG><a name="[f2]"></a>first_order_filter_cali</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, user_lib.o(i.first_order_filter_cali))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = first_order_filter_cali
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Rc_Control
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Keyboard_Move_Calculate
</UL>

<P><STRONG><a name="[e5]"></a>first_order_filter_init</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, user_lib.o(i.first_order_filter_init))
<BR><BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Init
</UL>

<P><STRONG><a name="[28c]"></a>flash_erase_address</STRONG> (Thumb, 42 bytes, Stack size 32 bytes, bsp_flash.o(i.flash_erase_address))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = flash_erase_address &rArr; HAL_FLASHEx_Erase &rArr; FLASH_WaitForLastOperation
</UL>
<BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASHEx_Erase
<LI><a href="#[29d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASH_Unlock
<LI><a href="#[29e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASH_Lock
<LI><a href="#[29c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ger_sector
</UL>
<BR>[Called By]<UL><LI><a href="#[28a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cali_data_write
</UL>

<P><STRONG><a name="[289]"></a>flash_read</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, bsp_flash.o(i.flash_read))
<BR><BR>[Calls]<UL><LI><a href="#[28b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[288]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cali_data_read
</UL>

<P><STRONG><a name="[28d]"></a>flash_write_single_address</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, bsp_flash.o(i.flash_write_single_address))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = flash_write_single_address &rArr; HAL_FLASH_Program &rArr; FLASH_WaitForLastOperation
</UL>
<BR>[Calls]<UL><LI><a href="#[29d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASH_Unlock
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASH_Program
<LI><a href="#[29e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASH_Lock
<LI><a href="#[29f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_next_flash_address
</UL>
<BR>[Called By]<UL><LI><a href="#[28a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cali_data_write
</UL>

<P><STRONG><a name="[100]"></a>fp32_constrain</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, user_lib.o(i.fp32_constrain))
<BR><BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_key_Contorl
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_Contorl
</UL>

<P><STRONG><a name="[22f]"></a>free</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, malloc.o(i.free))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = free
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CDC_DeInit
</UL>

<P><STRONG><a name="[319]"></a>get_CRC16_check_sum</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, CRC8_CRC16.o(i.get_CRC16_check_sum))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = get_CRC16_check_sum
</UL>
<BR>[Called By]<UL><LI><a href="#[2ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;verify_CRC16_check_sum
</UL>

<P><STRONG><a name="[31a]"></a>get_CRC8_check_sum</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, CRC8_CRC16.o(i.get_CRC8_check_sum))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = get_CRC8_check_sum
</UL>
<BR>[Called By]<UL><LI><a href="#[2fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;verify_CRC8_check_sum
</UL>

<P><STRONG><a name="[2b1]"></a>get_INS_angle_point</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, INS_task.o(i.get_INS_angle_point))
<BR><BR>[Called By]<UL><LI><a href="#[2ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_init
</UL>

<P><STRONG><a name="[1dd]"></a>get_angle</STRONG> (Thumb, 232 bytes, Stack size 24 bytes, ahrs.o(i.get_angle))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = get_angle &rArr; AHRS_atan2f &rArr; __hardfp_atan2f
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_atan2f
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_asinf
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_task
</UL>

<P><STRONG><a name="[27d]"></a>get_bullet_remaining</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, referee.o(i.get_bullet_remaining))
<BR><BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_update_g_Dynamic_2
</UL>

<P><STRONG><a name="[2b9]"></a>get_chassis_power_buffer</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, referee.o(i.get_chassis_power_buffer))
<BR><BR>[Called By]<UL><LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;girt_powerLimit
<LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;power_data_sent
</UL>

<P><STRONG><a name="[2b8]"></a>get_chassis_power_limit</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, referee.o(i.get_chassis_power_limit))
<BR><BR>[Called By]<UL><LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;girt_powerLimit
<LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;power_data_sent
</UL>

<P><STRONG><a name="[2bc]"></a>get_control_temperature</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, calibrate_task.o(i.get_control_temperature))
<BR><BR>[Called By]<UL><LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_temp_control
</UL>

<P><STRONG><a name="[277]"></a>get_enemy_hp1</STRONG> (Thumb, 28 bytes, Stack size 4 bytes, referee.o(i.get_enemy_hp1))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = get_enemy_hp1
</UL>
<BR>[Calls]<UL><LI><a href="#[2a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_red_or_blue
</UL>
<BR>[Called By]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_update_g_Dynamic_0
</UL>

<P><STRONG><a name="[278]"></a>get_enemy_hp2</STRONG> (Thumb, 28 bytes, Stack size 4 bytes, referee.o(i.get_enemy_hp2))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = get_enemy_hp2
</UL>
<BR>[Calls]<UL><LI><a href="#[2a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_red_or_blue
</UL>
<BR>[Called By]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_update_g_Dynamic_0
</UL>

<P><STRONG><a name="[279]"></a>get_enemy_hp3</STRONG> (Thumb, 28 bytes, Stack size 4 bytes, referee.o(i.get_enemy_hp3))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = get_enemy_hp3
</UL>
<BR>[Calls]<UL><LI><a href="#[2a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_red_or_blue
</UL>
<BR>[Called By]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_update_g_Dynamic_0
</UL>

<P><STRONG><a name="[27a]"></a>get_enemy_hp4</STRONG> (Thumb, 28 bytes, Stack size 4 bytes, referee.o(i.get_enemy_hp4))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = get_enemy_hp4
</UL>
<BR>[Calls]<UL><LI><a href="#[2a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_red_or_blue
</UL>
<BR>[Called By]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_update_g_Dynamic_0
</UL>

<P><STRONG><a name="[27b]"></a>get_enemy_hp7</STRONG> (Thumb, 28 bytes, Stack size 4 bytes, referee.o(i.get_enemy_hp7))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = get_enemy_hp7
</UL>
<BR>[Calls]<UL><LI><a href="#[2a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_red_or_blue
</UL>
<BR>[Called By]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_update_g_Dynamic_0
</UL>

<P><STRONG><a name="[305]"></a>get_error_list_point</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, detect_task.o(i.get_error_list_point))
<BR><BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_task
</UL>

<P><STRONG><a name="[27c]"></a>get_game_progress</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, referee.o(i.get_game_progress))
<BR><BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_update_g_Dynamic_2
</UL>

<P><STRONG><a name="[2b2]"></a>get_gyro_data_point</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, INS_task.o(i.get_gyro_data_point))
<BR><BR>[Called By]<UL><LI><a href="#[2ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_init
</UL>

<P><STRONG><a name="[29f]"></a>get_next_flash_address</STRONG> (Thumb, 168 bytes, Stack size 0 bytes, bsp_flash.o(i.get_next_flash_address))
<BR><BR>[Called By]<UL><LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_write_single_address
</UL>

<P><STRONG><a name="[2b0]"></a>get_pitch_gimbal_motor_measure_point</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, CAN_receive.o(i.get_pitch_gimbal_motor_measure_point))
<BR><BR>[Called By]<UL><LI><a href="#[2ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_init
</UL>

<P><STRONG><a name="[294]"></a>get_remote_control_point</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, remote_control.o(i.get_remote_control_point))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calibrate_task
<LI><a href="#[2ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_init
</UL>

<P><STRONG><a name="[308]"></a>get_robot_id</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, referee.o(i.get_robot_id))
<BR><BR>[Called By]<UL><LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_proc_string_frame
<LI><a href="#[26b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_proc_7_frame
<LI><a href="#[268]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_proc_5_frame
</UL>

<P><STRONG><a name="[222]"></a>get_shoot_cooling_limit</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, referee.o(i.get_shoot_cooling_limit))
<BR><BR>[Called By]<UL><LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SHOOT_NORMAL_Ctrl
</UL>

<P><STRONG><a name="[223]"></a>get_shoot_heat0</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, referee.o(i.get_shoot_heat0))
<BR><BR>[Called By]<UL><LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SHOOT_NORMAL_Ctrl
</UL>

<P><STRONG><a name="[114]"></a>get_shoot_speed</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, referee.o(i.get_shoot_speed))
<BR><BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Friction_SpeedFIX
</UL>

<P><STRONG><a name="[20a]"></a>get_temprate</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, bsp_adc.o(i.get_temprate))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = get_temprate &rArr; adcx_get_chx_value &rArr; HAL_ADC_PollForConversion
</UL>
<BR>[Calls]<UL><LI><a href="#[281]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adcx_get_chx_value
</UL>
<BR>[Called By]<UL><LI><a href="#[1a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cali_head_hook
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RC_cmd_to_calibrate
</UL>

<P><STRONG><a name="[276]"></a>get_vision_flag</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usb_task.o(i.get_vision_flag))
<BR><BR>[Called By]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_update_g_Dynamic_0
</UL>

<P><STRONG><a name="[2af]"></a>get_yaw_gimbal_motor_measure_point</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, CAN_receive.o(i.get_yaw_gimbal_motor_measure_point))
<BR><BR>[Called By]<UL><LI><a href="#[2ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_init
</UL>

<P><STRONG><a name="[2a2]"></a>gimbal_behaviour_control_set</STRONG> (Thumb, 72 bytes, Stack size 0 bytes, gimbal_behaviour.o(i.gimbal_behaviour_control_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = gimbal_behaviour_control_set &rArr; gimbal_init_control &rArr; __hardfp_fabs
</UL>
<BR>[Calls]<UL><LI><a href="#[2a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_zero_force_control
<LI><a href="#[2a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_relative_angle_control
<LI><a href="#[2a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_motionless_control
<LI><a href="#[2a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_init_control
<LI><a href="#[2a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_cali_control
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_absolute_angle_control
</UL>
<BR>[Called By]<UL><LI><a href="#[2b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_set_control
</UL>

<P><STRONG><a name="[2a9]"></a>gimbal_behaviour_mode_set</STRONG> (Thumb, 122 bytes, Stack size 8 bytes, gimbal_behaviour.o(i.gimbal_behaviour_mode_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = gimbal_behaviour_mode_set &rArr; gimbal_behavour_set &rArr; __hardfp_fabs
</UL>
<BR>[Calls]<UL><LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_behavour_set
</UL>
<BR>[Called By]<UL><LI><a href="#[2b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_set_mode
</UL>

<P><STRONG><a name="[83]"></a>gimbal_task</STRONG> (Thumb, 158 bytes, Stack size 0 bytes, gimbal_task.o(i.gimbal_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = gimbal_task &rArr; gimbal_control_loop &rArr; New_PID_cail &rArr; activation_function &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[298]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN_cmd_gimbal_yaw
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN_cmd_gimbal_pitch
<LI><a href="#[2b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;laser_on
<LI><a href="#[2b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_set_mode
<LI><a href="#[2b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_set_control
<LI><a href="#[2b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_mode_change_control_transit
<LI><a href="#[2ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_init
<LI><a href="#[2ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_feedback_update
<LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_control_loop
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;toe_is_error
</UL>
<BR>[Address Reference Count : 1]<UL><LI> freertos.o(.constdata)
</UL>
<P><STRONG><a name="[1d2]"></a>gyro_offset_calc</STRONG> (Thumb, 72 bytes, Stack size 0 bytes, INS_task.o(i.gyro_offset_calc))
<BR><BR>[Called By]<UL><LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_cali_gyro
</UL>

<P><STRONG><a name="[2bd]"></a>imu_pwm_set</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, bsp_imu_pwm.o(i.imu_pwm_set))
<BR><BR>[Called By]<UL><LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_temp_control
</UL>

<P><STRONG><a name="[2be]"></a>init_referee_struct_data</STRONG> (Thumb, 176 bytes, Stack size 16 bytes, referee.o(i.init_referee_struct_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = init_referee_struct_data
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;referee_usart_task
</UL>

<P><STRONG><a name="[2a0]"></a>is_red_or_blue</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, referee.o(i.is_red_or_blue))
<BR><BR>[Called By]<UL><LI><a href="#[27b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_enemy_hp7
<LI><a href="#[27a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_enemy_hp4
<LI><a href="#[279]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_enemy_hp3
<LI><a href="#[278]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_enemy_hp2
<LI><a href="#[277]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_enemy_hp1
<LI><a href="#[310]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_send_data
</UL>

<P><STRONG><a name="[2c4]"></a>ist8310_GPIO_init</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, ist8310driver_middleware.o(i.ist8310_GPIO_init))
<BR><BR>[Called By]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ist8310_init
</UL>

<P><STRONG><a name="[2bf]"></a>ist8310_IIC_read_single_reg</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, ist8310driver_middleware.o(i.ist8310_IIC_read_single_reg))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = ist8310_IIC_read_single_reg &rArr; HAL_I2C_Mem_Read &rArr; I2C_RequestMemoryRead &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ist8310_init
</UL>

<P><STRONG><a name="[2c0]"></a>ist8310_IIC_write_single_reg</STRONG> (Thumb, 30 bytes, Stack size 32 bytes, ist8310driver_middleware.o(i.ist8310_IIC_write_single_reg))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = ist8310_IIC_write_single_reg &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ist8310_init
</UL>

<P><STRONG><a name="[2c1]"></a>ist8310_RST_H</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, ist8310driver_middleware.o(i.ist8310_RST_H))
<BR><BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ist8310_init
</UL>

<P><STRONG><a name="[2c2]"></a>ist8310_RST_L</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, ist8310driver_middleware.o(i.ist8310_RST_L))
<BR><BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ist8310_init
</UL>

<P><STRONG><a name="[2c5]"></a>ist8310_com_init</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, ist8310driver_middleware.o(i.ist8310_com_init))
<BR><BR>[Called By]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ist8310_init
</UL>

<P><STRONG><a name="[2c3]"></a>ist8310_delay_ms</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, ist8310driver_middleware.o(i.ist8310_delay_ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = ist8310_delay_ms &rArr; osDelay &rArr; vTaskDelay &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
</UL>
<BR>[Called By]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ist8310_init
</UL>

<P><STRONG><a name="[1d3]"></a>ist8310_init</STRONG> (Thumb, 120 bytes, Stack size 24 bytes, ist8310driver.o(i.ist8310_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = ist8310_init &rArr; ist8310_IIC_write_single_reg &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ist8310_delay_ms
<LI><a href="#[2c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ist8310_com_init
<LI><a href="#[2c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ist8310_RST_L
<LI><a href="#[2c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ist8310_RST_H
<LI><a href="#[2c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ist8310_IIC_write_single_reg
<LI><a href="#[2bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ist8310_IIC_read_single_reg
<LI><a href="#[2c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ist8310_GPIO_init
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_task
</UL>

<P><STRONG><a name="[2b6]"></a>laser_on</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, bsp_laser.o(i.laser_on))
<BR><BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_task
</UL>

<P><STRONG><a name="[86]"></a>led_RGB_flow_task</STRONG> (Thumb, 274 bytes, Stack size 40 bytes, led_flow_task.o(i.led_RGB_flow_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = led_RGB_flow_task &rArr; LED_oncar &rArr; CAN_cmd_RGB &rArr; HAL_CAN_AddTxMessage
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
<LI><a href="#[2c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;aRGB_led_show
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_oncar
</UL>
<BR>[Address Reference Count : 1]<UL><LI> freertos.o(.constdata)
</UL>
<P><STRONG><a name="[ff]"></a>loop_fp32_constrain</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, user_lib.o(i.loop_fp32_constrain))
<BR><BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;New_PID_cail
<LI><a href="#[2a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_absolute_angle_limit
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_key_Contorl
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Set_Contorl
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;deal_err
</UL>

<P><STRONG><a name="[78]"></a>main</STRONG> (Thumb, 126 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 504<LI>Call Chain = main &rArr; MX_FREERTOS_Init &rArr; osThreadCreate &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[2c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osKernelStart
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_CRC_Init
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_CAN2_Init
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_CAN1_Init
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC3_Init
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
<LI><a href="#[2c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;remote_control_init
<LI><a href="#[2c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_init
<LI><a href="#[295]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_filter_init
<LI><a href="#[293]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cali_param_init
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART6_UART_Init
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM8_Init
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM5_Init
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM4_Init
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM1_Init
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM10_Init
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI1_Init
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RTC_Init
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_RNG_Init
<LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C3_Init
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C2_Init
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USB_DEVICE_Init
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FREERTOS_Init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[231]"></a>malloc</STRONG> (Thumb, 92 bytes, Stack size 20 bytes, malloc.o(i.malloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CDC_Init
</UL>

<P><STRONG><a name="[88]"></a>music_task</STRONG> (Thumb, 160 bytes, Stack size 0 bytes, music_task.o(i.music_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = music_task &rArr; osDelay &rArr; vTaskDelay &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
<LI><a href="#[2ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prepareBeepPacket
<LI><a href="#[284]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;beepUpdate
</UL>
<BR>[Address Reference Count : 1]<UL><LI> freertos.o(.constdata)
</UL>
<P><STRONG><a name="[c1]"></a>osDelay</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, cmsis_os.o(i.osDelay))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = osDelay &rArr; vTaskDelay &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[298]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_task
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_task
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;referee_usart_task
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;music_task
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_RGB_flow_task
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_task
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calibrate_task
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_task
<LI><a href="#[306]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_init
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_task
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_delay_ms
<LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ist8310_delay_ms
</UL>

<P><STRONG><a name="[2c9]"></a>osKernelStart</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, cmsis_os.o(i.osKernelStart))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = osKernelStart &rArr; vTaskStartScheduler &rArr; xTimerCreateTimerTask &rArr; xTaskCreateStatic &rArr; prvInitialiseNewTask
</UL>
<BR>[Calls]<UL><LI><a href="#[2cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1ec]"></a>osThreadCreate</STRONG> (Thumb, 88 bytes, Stack size 32 bytes, cmsis_os.o(i.osThreadCreate))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = osThreadCreate &rArr; xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;makeFreeRtosPriority
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreateStatic
<LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>
<BR>[Called By]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_FREERTOS_Init
</UL>

<P><STRONG><a name="[1d5]"></a>pcTaskGetName</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, tasks.o(i.pcTaskGetName))
<BR><BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_task
</UL>

<P><STRONG><a name="[1e3]"></a>pm01_get_vout</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, pm01_api.o(i.pm01_get_vout))
<BR><BR>[Called By]<UL><LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_oncar
</UL>

<P><STRONG><a name="[135]"></a>pm01_response_handle</STRONG> (Thumb, 240 bytes, Stack size 8 bytes, pm01_api.o(i.pm01_response_handle))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = pm01_response_handle
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CAN_RxFifo0MsgPendingCallback
</UL>

<P><STRONG><a name="[299]"></a>power_data_sent</STRONG> (Thumb, 158 bytes, Stack size 16 bytes, chassis_task.o(i.power_data_sent))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = power_data_sent &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN_cmd_power
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[2b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_chassis_power_limit
<LI><a href="#[2b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_chassis_power_buffer
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chassis_task
</UL>

<P><STRONG><a name="[2ca]"></a>prepareBeepPacket</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, music_task.o(i.prepareBeepPacket))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = prepareBeepPacket
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;music_task
</UL>

<P><STRONG><a name="[2f7]"></a>pvPortMalloc</STRONG> (Thumb, 208 bytes, Stack size 24 bytes, heap_4.o(i.pvPortMalloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[2f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertBlockIntoFreeList
<LI><a href="#[2f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHeapInit
<LI><a href="#[2f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[2ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
</UL>
<BR>[Called By]<UL><LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[30b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
</UL>

<P><STRONG><a name="[322]"></a>pvTaskIncrementMutexHeldCount</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, tasks.o(i.pvTaskIncrementMutexHeldCount))
<BR><BR>[Called By]<UL><LI><a href="#[30d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
</UL>

<P><STRONG><a name="[2e5]"></a>pxPortInitialiseStack</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, port.o(i.pxPortInitialiseStack))
<BR><BR>[Called By]<UL><LI><a href="#[2e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
</UL>

<P><STRONG><a name="[2fa]"></a>referee_data_solve</STRONG> (Thumb, 470 bytes, Stack size 16 bytes, referee.o(i.referee_data_solve))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = referee_data_solve
</UL>
<BR>[Calls]<UL><LI><a href="#[2fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JUDGE_ShootNumCount
<LI><a href="#[28b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[2fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;referee_unpack_fifo_data
</UL>

<P><STRONG><a name="[89]"></a>referee_usart_task</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, referee_usart_task.o(i.referee_usart_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = referee_usart_task &rArr; osDelay &rArr; vTaskDelay &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
<LI><a href="#[302]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart6_init
<LI><a href="#[301]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fifo_s_init
<LI><a href="#[2fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;referee_unpack_fifo_data
<LI><a href="#[2be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_referee_struct_data
</UL>
<BR>[Address Reference Count : 1]<UL><LI> freertos.o(.constdata)
</UL>
<P><STRONG><a name="[2c8]"></a>remote_control_init</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, remote_control.o(i.remote_control_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = remote_control_init &rArr; RC_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[303]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[28e]"></a>set_cali_gimbal_hook</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, gimbal_task.o(i.set_cali_gimbal_hook))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = set_cali_gimbal_hook &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cali_gimbal_hook
</UL>

<P><STRONG><a name="[85]"></a>shoot_task</STRONG> (Thumb, 86 bytes, Stack size 0 bytes, shoot_task.o(i.shoot_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = shoot_task &rArr; Fric_Speed_PID_Calculate &rArr; Fric_SpeedLoop &rArr; New_PID_cail &rArr; activation_function &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
<LI><a href="#[298]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SHOOT_InitArgument
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SHOOT_CANbusCtrlMotor
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOL_SpeedLoop
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOL_PositionLoop
<LI><a href="#[304]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_Rest
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_Rc_Ctrl
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_Key_Ctrl
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fric_Speed_PID_Calculate
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fric_Power_Change
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fric_Key_Ctrl
</UL>
<BR>[Address Reference Count : 1]<UL><LI> freertos.o(.constdata)
</UL>
<P><STRONG><a name="[257]"></a>sqrtf</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, sqrtf_full.o(i.sqrtf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[25b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asinf
</UL>

<P><STRONG><a name="[2ba]"></a>square</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, chassis_task.o(i.square))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = square &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;girt_powerLimit
</UL>

<P><STRONG><a name="[7f]"></a>test_task</STRONG> (Thumb, 94 bytes, Stack size 0 bytes, test_task.o(i.test_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = test_task &rArr; osDelay &rArr; vTaskDelay &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
<LI><a href="#[305]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_error_list_point
<LI><a href="#[285]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buzzer_warn_error
</UL>
<BR>[Address Reference Count : 1]<UL><LI> freertos.o(.constdata)
</UL>
<P><STRONG><a name="[e2]"></a>toe_is_error</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, detect_task.o(i.toe_is_error))
<BR><BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_task
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_oncar
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_behavour_set
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CHASSIS_CANSend
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SHOOT_CANbusCtrlMotor
</UL>

<P><STRONG><a name="[268]"></a>ui_proc_5_frame</STRONG> (Thumb, 82 bytes, Stack size 8 bytes, ui_interface.o(i.ui_proc_5_frame))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ui_proc_5_frame &rArr; calc_crc8
</UL>
<BR>[Calls]<UL><LI><a href="#[308]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_robot_id
<LI><a href="#[307]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_crc8
<LI><a href="#[309]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_crc16
</UL>
<BR>[Called By]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_update_g_Dynamic_0
<LI><a href="#[275]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Ungroup_5
<LI><a href="#[267]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Dynamic_0
</UL>

<P><STRONG><a name="[26b]"></a>ui_proc_7_frame</STRONG> (Thumb, 82 bytes, Stack size 8 bytes, ui_interface.o(i.ui_proc_7_frame))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ui_proc_7_frame &rArr; calc_crc8
</UL>
<BR>[Calls]<UL><LI><a href="#[308]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_robot_id
<LI><a href="#[307]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_crc8
<LI><a href="#[309]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_crc16
</UL>
<BR>[Called By]<UL><LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_update_g_Dynamic_3
<LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_update_g_Dynamic_2
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_update_g_Dynamic_1
<LI><a href="#[26d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Dynamic_3
<LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Dynamic_2
<LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Dynamic_1
</UL>

<P><STRONG><a name="[270]"></a>ui_proc_string_frame</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, ui_interface.o(i.ui_proc_string_frame))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ui_proc_string_frame &rArr; calc_crc8
</UL>
<BR>[Calls]<UL><LI><a href="#[308]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_robot_id
<LI><a href="#[307]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_crc8
<LI><a href="#[309]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_crc16
<LI><a href="#[30a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[274]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Ungroup_4
<LI><a href="#[273]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Ungroup_3
<LI><a href="#[272]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Ungroup_2
<LI><a href="#[271]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Ungroup_1
<LI><a href="#[26e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Ungroup_0
</UL>

<P><STRONG><a name="[87]"></a>ui_task</STRONG> (Thumb, 116 bytes, Stack size 0 bytes, UI_task.o(i.ui_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = ui_task &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[30d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[30c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[30b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
<LI><a href="#[298]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
<LI><a href="#[306]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> freertos.o(.constdata)
</UL>
<P><STRONG><a name="[1d8]"></a>ulTaskNotifyTake</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, tasks.o(i.ulTaskNotifyTake))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = ulTaskNotifyTake &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[2d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[2cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_task
</UL>

<P><STRONG><a name="[302]"></a>usart6_init</STRONG> (Thumb, 186 bytes, Stack size 20 bytes, bsp_usart.o(i.usart6_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = usart6_init
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;referee_usart_task
</UL>

<P><STRONG><a name="[269]"></a>usart6_tx_dma_enable</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, bsp_usart.o(i.usart6_tx_dma_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usart6_tx_dma_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_update_g_Dynamic_3
<LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_update_g_Dynamic_2
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_update_g_Dynamic_1
<LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_update_g_Dynamic_0
<LI><a href="#[275]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Ungroup_5
<LI><a href="#[274]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Ungroup_4
<LI><a href="#[273]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Ungroup_3
<LI><a href="#[272]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Ungroup_2
<LI><a href="#[271]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Ungroup_1
<LI><a href="#[26e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Ungroup_0
<LI><a href="#[26d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Dynamic_3
<LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Dynamic_2
<LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Dynamic_1
<LI><a href="#[267]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Dynamic_0
</UL>

<P><STRONG><a name="[dd]"></a>usb_read_data</STRONG> (Thumb, 192 bytes, Stack size 16 bytes, usb_task.o(i.usb_read_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usb_read_data
</UL>
<BR>[Calls]<UL><LI><a href="#[30e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_isnanf
<LI><a href="#[30f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vision_Limit
<LI><a href="#[28b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CDC_Receive_FS
</UL>

<P><STRONG><a name="[310]"></a>usb_send_data</STRONG> (Thumb, 94 bytes, Stack size 8 bytes, usb_task.o(i.usb_send_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = usb_send_data &rArr; CDC_Transmit_FS &rArr; USBD_CDC_TransmitPacket &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CDC_Transmit_FS
<LI><a href="#[2a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_red_or_blue
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_task
</UL>

<P><STRONG><a name="[8a]"></a>usb_task</STRONG> (Thumb, 64 bytes, Stack size 0 bytes, usb_task.o(i.usb_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = usb_task &rArr; usb_send_data &rArr; CDC_Transmit_FS &rArr; USBD_CDC_TransmitPacket &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
<LI><a href="#[310]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_send_data
</UL>
<BR>[Address Reference Count : 1]<UL><LI> freertos.o(.constdata)
</UL>
<P><STRONG><a name="[2d0]"></a>uxListRemove</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, list.o(i.uxListRemove))
<BR><BR>[Called By]<UL><LI><a href="#[2f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[324]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityInherit
<LI><a href="#[2df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityDisinherit
<LI><a href="#[315]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPriorityDisinheritAfterTimeout
<LI><a href="#[31e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
<LI><a href="#[2f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[2f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSwitchTimerLists
<LI><a href="#[2eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[2e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessExpiredTimer
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskNotifyGiveFromISR
<LI><a href="#[2db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckTasksWaitingTermination
<LI><a href="#[2cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
</UL>

<P><STRONG><a name="[316]"></a>vApplicationGetIdleTaskMemory</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, freertos.o(i.vApplicationGetIdleTaskMemory))
<BR><BR>[Called By]<UL><LI><a href="#[2cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
</UL>

<P><STRONG><a name="[327]"></a>vApplicationGetTimerTaskMemory</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, freertos.o(i.vApplicationGetTimerTaskMemory))
<BR><BR>[Called By]<UL><LI><a href="#[317]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
</UL>

<P><STRONG><a name="[2d8]"></a>vListInitialise</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, list.o(i.vListInitialise))
<BR><BR>[Called By]<UL><LI><a href="#[2e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
<LI><a href="#[2d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
<LI><a href="#[2d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseTaskLists
</UL>

<P><STRONG><a name="[2e4]"></a>vListInitialiseItem</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, list.o(i.vListInitialiseItem))
<BR><BR>[Called By]<UL><LI><a href="#[2e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
</UL>

<P><STRONG><a name="[2d2]"></a>vListInsert</STRONG> (Thumb, 48 bytes, Stack size 12 bytes, list.o(i.vListInsert))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = vListInsert
</UL>
<BR>[Called By]<UL><LI><a href="#[314]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[2f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSwitchTimerLists
<LI><a href="#[2e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertTimerInActiveList
<LI><a href="#[2cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
</UL>

<P><STRONG><a name="[2d1]"></a>vListInsertEnd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, list.o(i.vListInsertEnd))
<BR><BR>[Called By]<UL><LI><a href="#[2f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[324]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityInherit
<LI><a href="#[2df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityDisinherit
<LI><a href="#[315]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPriorityDisinheritAfterTimeout
<LI><a href="#[313]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventListRestricted
<LI><a href="#[31e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
<LI><a href="#[2f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskNotifyGiveFromISR
<LI><a href="#[2d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
<LI><a href="#[2cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
</UL>

<P><STRONG><a name="[2d4]"></a>vPortEnterCritical</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, port.o(i.vPortEnterCritical))
<BR><BR>[Called By]<UL><LI><a href="#[320]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[2e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
<LI><a href="#[2f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
<LI><a href="#[2f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[2e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueFull
<LI><a href="#[2e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueEmpty
<LI><a href="#[2f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[30d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[2ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[30c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[2d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTaskNotifyTake
<LI><a href="#[2db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckTasksWaitingTermination
<LI><a href="#[2d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
</UL>

<P><STRONG><a name="[2d6]"></a>vPortExitCritical</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, port.o(i.vPortExitCritical))
<BR><BR>[Called By]<UL><LI><a href="#[320]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[2e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
<LI><a href="#[2f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
<LI><a href="#[2f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[2e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueFull
<LI><a href="#[2e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueEmpty
<LI><a href="#[2f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[30d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[2ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[30c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[2d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTaskNotifyTake
<LI><a href="#[2db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckTasksWaitingTermination
<LI><a href="#[2d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
</UL>

<P><STRONG><a name="[2e0]"></a>vPortFree</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, heap_4.o(i.vPortFree))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[2f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertBlockIntoFreeList
<LI><a href="#[2f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[2ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
</UL>
<BR>[Called By]<UL><LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[2eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[2dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvDeleteTCB
</UL>

<P><STRONG><a name="[31b]"></a>vPortSetupTimerInterrupt</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, port.o(i.vPortSetupTimerInterrupt))
<BR><BR>[Called By]<UL><LI><a href="#[318]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
</UL>

<P><STRONG><a name="[311]"></a>vPortValidateInterruptPriority</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, port.o(i.vPortValidateInterruptPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = vPortValidateInterruptPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[312]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortGetIPSR
</UL>
<BR>[Called By]<UL><LI><a href="#[321]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskNotifyGiveFromISR
</UL>

<P><STRONG><a name="[2da]"></a>vQueueAddToRegistry</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, queue.o(i.vQueueAddToRegistry))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = vQueueAddToRegistry
</UL>
<BR>[Called By]<UL><LI><a href="#[2d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
</UL>

<P><STRONG><a name="[2f1]"></a>vQueueWaitForMessageRestricted</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, queue.o(i.vQueueWaitForMessageRestricted))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = vQueueWaitForMessageRestricted &rArr; vTaskPlaceOnEventListRestricted &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[313]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventListRestricted
<LI><a href="#[2f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[2d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[2ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
</UL>

<P><STRONG><a name="[298]"></a>vTaskDelay</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, tasks.o(i.vTaskDelay))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = vTaskDelay &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[2f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[2ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[2cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_task
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shoot_task
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_task
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;detect_task
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chassis_task
</UL>

<P><STRONG><a name="[31f]"></a>vTaskInternalSetTimeOutState</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, tasks.o(i.vTaskInternalSetTimeOutState))
<BR><BR>[Called By]<UL><LI><a href="#[320]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[30d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[2ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[30c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[2f6]"></a>vTaskMissedYield</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, tasks.o(i.vTaskMissedYield))
<BR><BR>[Called By]<UL><LI><a href="#[2f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
</UL>

<P><STRONG><a name="[14f]"></a>vTaskNotifyGiveFromISR</STRONG> (Thumb, 180 bytes, Stack size 24 bytes, tasks.o(i.vTaskNotifyGiveFromISR))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = vTaskNotifyGiveFromISR &rArr; vPortValidateInterruptPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[311]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortValidateInterruptPriority
<LI><a href="#[2d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_Callback
</UL>

<P><STRONG><a name="[314]"></a>vTaskPlaceOnEventList</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, tasks.o(i.vTaskPlaceOnEventList))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = vTaskPlaceOnEventList &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[2d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
<LI><a href="#[2cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
</UL>
<BR>[Called By]<UL><LI><a href="#[30d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[2ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[30c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[313]"></a>vTaskPlaceOnEventListRestricted</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, tasks.o(i.vTaskPlaceOnEventListRestricted))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = vTaskPlaceOnEventListRestricted &rArr; prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[2d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[2cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
</UL>
<BR>[Called By]<UL><LI><a href="#[2f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
</UL>

<P><STRONG><a name="[315]"></a>vTaskPriorityDisinheritAfterTimeout</STRONG> (Thumb, 164 bytes, Stack size 16 bytes, tasks.o(i.vTaskPriorityDisinheritAfterTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = vTaskPriorityDisinheritAfterTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[2d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[30d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
</UL>

<P><STRONG><a name="[2cb]"></a>vTaskStartScheduler</STRONG> (Thumb, 126 bytes, Stack size 40 bytes, tasks.o(i.vTaskStartScheduler))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = vTaskStartScheduler &rArr; xTimerCreateTimerTask &rArr; xTaskCreateStatic &rArr; prvInitialiseNewTask
</UL>
<BR>[Calls]<UL><LI><a href="#[318]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreateStatic
<LI><a href="#[317]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
<LI><a href="#[316]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vApplicationGetIdleTaskMemory
</UL>
<BR>[Called By]<UL><LI><a href="#[2c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osKernelStart
</UL>

<P><STRONG><a name="[2ef]"></a>vTaskSuspendAll</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, tasks.o(i.vTaskSuspendAll))
<BR><BR>[Called By]<UL><LI><a href="#[30d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[2ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[30c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[298]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[2e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[2f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[2ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetHandle
</UL>

<P><STRONG><a name="[8d]"></a>vTaskSwitchContext</STRONG> (Thumb, 86 bytes, Stack size 0 bytes, tasks.o(i.vTaskSwitchContext))
<BR><BR>[Called By]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>

<P><STRONG><a name="[2ff]"></a>verify_CRC16_check_sum</STRONG> (Thumb, 60 bytes, Stack size 12 bytes, CRC8_CRC16.o(i.verify_CRC16_check_sum))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = verify_CRC16_check_sum &rArr; get_CRC16_check_sum
</UL>
<BR>[Calls]<UL><LI><a href="#[319]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_CRC16_check_sum
</UL>
<BR>[Called By]<UL><LI><a href="#[2fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;referee_unpack_fifo_data
</UL>

<P><STRONG><a name="[2fe]"></a>verify_CRC8_check_sum</STRONG> (Thumb, 46 bytes, Stack size 12 bytes, CRC8_CRC16.o(i.verify_CRC8_check_sum))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = verify_CRC8_check_sum &rArr; get_CRC8_check_sum
</UL>
<BR>[Calls]<UL><LI><a href="#[31a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_CRC8_check_sum
</UL>
<BR>[Called By]<UL><LI><a href="#[2fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;referee_unpack_fifo_data
</UL>

<P><STRONG><a name="[318]"></a>xPortStartScheduler</STRONG> (Thumb, 222 bytes, Stack size 16 bytes, port.o(i.xPortStartScheduler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xPortStartScheduler
</UL>
<BR>[Calls]<UL><LI><a href="#[31b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortSetupTimerInterrupt
<LI><a href="#[31c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___6_port_c_39a90d8d__prvEnableVFP
<LI><a href="#[31d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___6_port_c_39a90d8d__prvStartFirstTask
</UL>
<BR>[Called By]<UL><LI><a href="#[2cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
</UL>

<P><STRONG><a name="[226]"></a>xPortSysTickHandler</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, port.o(i.xPortSysTickHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = xPortSysTickHandler &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[31e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
</UL>
<BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[30b]"></a>xQueueGenericCreate</STRONG> (Thumb, 74 bytes, Stack size 24 bytes, queue.o(i.xQueueGenericCreate))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = xQueueGenericCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[2e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewQueue
<LI><a href="#[2f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_task
</UL>

<P><STRONG><a name="[2d9]"></a>xQueueGenericCreateStatic</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, queue.o(i.xQueueGenericCreateStatic))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = xQueueGenericCreateStatic &rArr; prvInitialiseNewQueue &rArr; xQueueGenericReset &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[2e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewQueue
</UL>
<BR>[Called By]<UL><LI><a href="#[2d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
</UL>

<P><STRONG><a name="[2e2]"></a>xQueueGenericReset</STRONG> (Thumb, 134 bytes, Stack size 16 bytes, queue.o(i.xQueueGenericReset))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = xQueueGenericReset &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[2f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[2d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
<LI><a href="#[2d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[2e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewQueue
</UL>

<P><STRONG><a name="[30c]"></a>xQueueGenericSend</STRONG> (Thumb, 352 bytes, Stack size 56 bytes, queue.o(i.xQueueGenericSend))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = xQueueGenericSend &rArr; prvUnlockQueue &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[2f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[320]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[314]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[31f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskInternalSetTimeOutState
<LI><a href="#[2f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[2e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueFull
<LI><a href="#[2de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataToQueue
<LI><a href="#[2f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
<LI><a href="#[2ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[2d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[2ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_task
</UL>

<P><STRONG><a name="[321]"></a>xQueueGenericSendFromISR</STRONG> (Thumb, 190 bytes, Stack size 32 bytes, queue.o(i.xQueueGenericSendFromISR))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = xQueueGenericSendFromISR &rArr; prvCopyDataToQueue &rArr; xTaskPriorityDisinherit
</UL>
<BR>[Calls]<UL><LI><a href="#[2f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[2de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataToQueue
<LI><a href="#[311]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortValidateInterruptPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[2ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
</UL>

<P><STRONG><a name="[2ed]"></a>xQueueReceive</STRONG> (Thumb, 310 bytes, Stack size 56 bytes, queue.o(i.xQueueReceive))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = xQueueReceive &rArr; prvUnlockQueue &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[2f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[320]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[314]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[31f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskInternalSetTimeOutState
<LI><a href="#[2f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[2e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueEmpty
<LI><a href="#[2dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataFromQueue
<LI><a href="#[2f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
<LI><a href="#[2ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[2d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[2eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
</UL>

<P><STRONG><a name="[30d]"></a>xQueueSemaphoreTake</STRONG> (Thumb, 376 bytes, Stack size 48 bytes, queue.o(i.xQueueSemaphoreTake))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = xQueueSemaphoreTake &rArr; prvUnlockQueue &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[2f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[324]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityInherit
<LI><a href="#[320]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[315]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPriorityDisinheritAfterTimeout
<LI><a href="#[314]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[31f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskInternalSetTimeOutState
<LI><a href="#[322]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvTaskIncrementMutexHeldCount
<LI><a href="#[2f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[2e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueEmpty
<LI><a href="#[323]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvGetDisinheritPriorityAfterTimeout
<LI><a href="#[2f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
<LI><a href="#[2ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[2d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_task
</UL>

<P><STRONG><a name="[320]"></a>xTaskCheckForTimeOut</STRONG> (Thumb, 140 bytes, Stack size 24 bytes, tasks.o(i.xTaskCheckForTimeOut))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = xTaskCheckForTimeOut
</UL>
<BR>[Calls]<UL><LI><a href="#[31f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskInternalSetTimeOutState
<LI><a href="#[2d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[30d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[2ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[30c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[2ce]"></a>xTaskCreate</STRONG> (Thumb, 100 bytes, Stack size 56 bytes, tasks.o(i.xTaskCreate))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = xTaskCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[2e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[2f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[2e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
<LI><a href="#[2d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
</UL>
<BR>[Called By]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osThreadCreate
</UL>

<P><STRONG><a name="[2cd]"></a>xTaskCreateStatic</STRONG> (Thumb, 92 bytes, Stack size 40 bytes, tasks.o(i.xTaskCreateStatic))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = xTaskCreateStatic &rArr; prvInitialiseNewTask
</UL>
<BR>[Calls]<UL><LI><a href="#[2e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
<LI><a href="#[2d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
</UL>
<BR>[Called By]<UL><LI><a href="#[2cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osThreadCreate
<LI><a href="#[317]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
</UL>

<P><STRONG><a name="[1d6]"></a>xTaskGetHandle</STRONG> (Thumb, 128 bytes, Stack size 24 bytes, tasks.o(i.xTaskGetHandle))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = xTaskGetHandle &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[2f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[2ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[325]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSearchForNameWithinSingleList
<LI><a href="#[30a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_task
</UL>

<P><STRONG><a name="[14e]"></a>xTaskGetSchedulerState</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, tasks.o(i.xTaskGetSchedulerState))
<BR><BR>[Called By]<UL><LI><a href="#[2ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
<LI><a href="#[30d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[2ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[30c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_Callback
</UL>

<P><STRONG><a name="[ee]"></a>xTaskGetTickCount</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, tasks.o(i.xTaskGetTickCount))
<BR><BR>[Called By]<UL><LI><a href="#[2ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;shoot_task
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;detect_task
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;detect_hook
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RC_cmd_to_calibrate
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Chassis_Keyboard_Move_Calculate
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SHOOT_SINGLE_Ctrl
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SHOOT_NORMAL_Ctrl
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SHOOT_MIDF_HIGHTS_Ctrl
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SHOOT_BUFF_Ctrl
<LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_KeyPosiCtrl
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fric_Power_Change
</UL>

<P><STRONG><a name="[31e]"></a>xTaskIncrementTick</STRONG> (Thumb, 196 bytes, Stack size 24 bytes, tasks.o(i.xTaskIncrementTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[2d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[326]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvResetNextTaskUnblockTime
</UL>
<BR>[Called By]<UL><LI><a href="#[2f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortSysTickHandler
</UL>

<P><STRONG><a name="[2df]"></a>xTaskPriorityDisinherit</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, tasks.o(i.xTaskPriorityDisinherit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xTaskPriorityDisinherit
</UL>
<BR>[Calls]<UL><LI><a href="#[2d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[2de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataToQueue
</UL>

<P><STRONG><a name="[324]"></a>xTaskPriorityInherit</STRONG> (Thumb, 142 bytes, Stack size 16 bytes, tasks.o(i.xTaskPriorityInherit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xTaskPriorityInherit
</UL>
<BR>[Calls]<UL><LI><a href="#[2d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[30d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
</UL>

<P><STRONG><a name="[2f5]"></a>xTaskRemoveFromEventList</STRONG> (Thumb, 114 bytes, Stack size 24 bytes, tasks.o(i.xTaskRemoveFromEventList))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[2d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[2e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
<LI><a href="#[2f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[30d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[2ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[321]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
<LI><a href="#[30c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[2f0]"></a>xTaskResumeAll</STRONG> (Thumb, 192 bytes, Stack size 24 bytes, tasks.o(i.xTaskResumeAll))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[31e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
<LI><a href="#[2d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[2d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[326]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvResetNextTaskUnblockTime
</UL>
<BR>[Called By]<UL><LI><a href="#[30d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[2ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[30c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[298]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[2e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[2f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[2ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetHandle
</UL>

<P><STRONG><a name="[317]"></a>xTimerCreateTimerTask</STRONG> (Thumb, 86 bytes, Stack size 40 bytes, timers.o(i.xTimerCreateTimerTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = xTimerCreateTimerTask &rArr; xTaskCreateStatic &rArr; prvInitialiseNewTask
</UL>
<BR>[Calls]<UL><LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreateStatic
<LI><a href="#[327]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vApplicationGetTimerTaskMemory
<LI><a href="#[2d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
</UL>
<BR>[Called By]<UL><LI><a href="#[2cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
</UL>

<P><STRONG><a name="[2ea]"></a>xTimerGenericCommand</STRONG> (Thumb, 104 bytes, Stack size 40 bytes, timers.o(i.xTimerGenericCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = xTimerGenericCommand &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
<LI><a href="#[321]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
<LI><a href="#[30c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>
<BR>[Called By]<UL><LI><a href="#[2f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSwitchTimerLists
<LI><a href="#[2eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[2e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessExpiredTimer
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[11f]"></a>ADC_Init</STRONG> (Thumb, 298 bytes, Stack size 0 bytes, stm32f4xx_hal_adc.o(i.ADC_Init))
<BR><BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
</UL>

<P><STRONG><a name="[161]"></a>__NVIC_SetPriority</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority))
<BR><BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>

<P><STRONG><a name="[142]"></a>DMA_CalcBaseAndBitshift</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift))
<BR><BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
</UL>

<P><STRONG><a name="[141]"></a>DMA_CheckFifoParam</STRONG> (Thumb, 120 bytes, Stack size 8 bytes, stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA_CheckFifoParam
</UL>
<BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
</UL>

<P><STRONG><a name="[14a]"></a>FLASH_Program_Byte</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_hal_flash.o(i.FLASH_Program_Byte))
<BR><BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASH_Program
</UL>

<P><STRONG><a name="[149]"></a>FLASH_Program_DoubleWord</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = FLASH_Program_DoubleWord
</UL>
<BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASH_Program
</UL>

<P><STRONG><a name="[14b]"></a>FLASH_Program_HalfWord</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord))
<BR><BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASH_Program
</UL>

<P><STRONG><a name="[14c]"></a>FLASH_Program_Word</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, stm32f4xx_hal_flash.o(i.FLASH_Program_Word))
<BR><BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASH_Program
</UL>

<P><STRONG><a name="[109]"></a>FLASH_SetErrorCode</STRONG> (Thumb, 96 bytes, Stack size 0 bytes, stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode))
<BR><BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_WaitForLastOperation
</UL>

<P><STRONG><a name="[145]"></a>FLASH_MassErase</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase))
<BR><BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_FLASHEx_Erase
</UL>

<P><STRONG><a name="[1d0]"></a>I2C_IsAcknowledgeFailed</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed))
<BR><BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
</UL>

<P><STRONG><a name="[155]"></a>I2C_RequestMemoryRead</STRONG> (Thumb, 288 bytes, Stack size 40 bytes, stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = I2C_RequestMemoryRead &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnMasterAddressFlagUntilTimeout
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Read
</UL>

<P><STRONG><a name="[158]"></a>I2C_RequestMemoryWrite</STRONG> (Thumb, 178 bytes, Stack size 32 bytes, stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnMasterAddressFlagUntilTimeout
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>

<P><STRONG><a name="[15a]"></a>I2C_WaitOnBTFFlagUntilTimeout</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_WaitOnBTFFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_IsAcknowledgeFailed
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>

<P><STRONG><a name="[154]"></a>I2C_WaitOnFlagUntilTimeout</STRONG> (Thumb, 122 bytes, Stack size 32 bytes, stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Read
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryRead
</UL>

<P><STRONG><a name="[1cf]"></a>I2C_WaitOnMasterAddressFlagUntilTimeout</STRONG> (Thumb, 168 bytes, Stack size 32 bytes, stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryRead
</UL>

<P><STRONG><a name="[156]"></a>I2C_WaitOnRXNEFlagUntilTimeout</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = I2C_WaitOnRXNEFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Read
</UL>

<P><STRONG><a name="[159]"></a>I2C_WaitOnTXEFlagUntilTimeout</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_WaitOnTXEFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_IsAcknowledgeFailed
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryRead
</UL>

<P><STRONG><a name="[17d]"></a>PCD_EP_OutSetupPacket_int</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, stm32f4xx_hal_pcd.o(i.PCD_EP_OutSetupPacket_int))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = PCD_EP_OutSetupPacket_int &rArr; HAL_PCD_SetupStageCallback &rArr; USBD_LL_SetupStage &rArr; USBD_StdDevReq &rArr; USBD_GetDescriptor &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_EP0_OutStart
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_SetupStageCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[17c]"></a>PCD_EP_OutXfrComplete_int</STRONG> (Thumb, 222 bytes, Stack size 24 bytes, stm32f4xx_hal_pcd.o(i.PCD_EP_OutXfrComplete_int))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = PCD_EP_OutXfrComplete_int &rArr; HAL_PCD_SetupStageCallback &rArr; USBD_LL_SetupStage &rArr; USBD_StdDevReq &rArr; USBD_GetDescriptor &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_EP0_OutStart
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_SetupStageCallback
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_DataOutStageCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[180]"></a>PCD_WriteEmptyTxFifo</STRONG> (Thumb, 156 bytes, Stack size 40 bytes, stm32f4xx_hal_pcd.o(i.PCD_WriteEmptyTxFifo))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = PCD_WriteEmptyTxFifo &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_WritePacket
</UL>
<BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PCD_IRQHandler
</UL>

<P><STRONG><a name="[1b1]"></a>SPI_EndRxTxTransaction</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
</UL>

<P><STRONG><a name="[224]"></a>SPI_WaitFlagStateUntilTimeout</STRONG> (Thumb, 146 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
</UL>

<P><STRONG><a name="[1b8]"></a>TIM_ITRx_SetConfig</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig))
<BR><BR>[Called By]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[1be]"></a>TIM_OC1_SetConfig</STRONG> (Thumb, 96 bytes, Stack size 20 bytes, stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_OC1_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
</UL>

<P><STRONG><a name="[1c0]"></a>TIM_OC3_SetConfig</STRONG> (Thumb, 104 bytes, Stack size 20 bytes, stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_OC3_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
</UL>

<P><STRONG><a name="[1c1]"></a>TIM_OC4_SetConfig</STRONG> (Thumb, 74 bytes, Stack size 12 bytes, stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TIM_OC4_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_ConfigChannel
</UL>

<P><STRONG><a name="[1b7]"></a>TIM_TI1_ConfigInputStage</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_TI1_ConfigInputStage
</UL>
<BR>[Called By]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[1b9]"></a>TIM_TI2_ConfigInputStage</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_TI2_ConfigInputStage
</UL>
<BR>[Called By]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[7b]"></a>UART_DMAAbortOnError</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART_DMAAbortOnError
</UL>
<BR>[Calls]<UL><LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
</UL>
<P><STRONG><a name="[1c7]"></a>UART_EndRxTransfer</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.UART_EndRxTransfer))
<BR><BR>[Called By]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[1cb]"></a>UART_EndTransmit_IT</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART_EndTransmit_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[1c6]"></a>UART_Receive_IT</STRONG> (Thumb, 156 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_Receive_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART_Receive_IT
</UL>
<BR>[Calls]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[1ce]"></a>UART_SetConfig</STRONG> (Thumb, 780 bytes, Stack size 24 bytes, stm32f4xx_hal_uart.o(i.UART_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = UART_SetConfig &rArr; HAL_RCC_GetPCLK2Freq
</UL>
<BR>[Calls]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
</UL>
<BR>[Called By]<UL><LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[1ca]"></a>UART_Transmit_IT</STRONG> (Thumb, 102 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.UART_Transmit_IT))
<BR><BR>[Called By]<UL><LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[254]"></a>USB_CoreReset</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, stm32f4xx_ll_usb.o(i.USB_CoreReset))
<BR><BR>[Called By]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_CoreInit
</UL>

<P><STRONG><a name="[5]"></a>USBD_CDC_DataIn</STRONG> (Thumb, 76 bytes, Stack size 24 bytes, usbd_cdc.o(i.USBD_CDC_DataIn))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = USBD_CDC_DataIn &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_Transmit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_cdc.o(.data)
</UL>
<P><STRONG><a name="[6]"></a>USBD_CDC_DataOut</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, usbd_cdc.o(i.USBD_CDC_DataOut))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USBD_CDC_DataOut
</UL>
<BR>[Calls]<UL><LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_GetRxDataSize
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_cdc.o(.data)
</UL>
<P><STRONG><a name="[2]"></a>USBD_CDC_DeInit</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, usbd_cdc.o(i.USBD_CDC_DeInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = USBD_CDC_DeInit &rArr; USBD_LL_CloseEP &rArr; HAL_PCD_EP_Close &rArr; USB_DeactivateEndpoint
</UL>
<BR>[Calls]<UL><LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_CloseEP
<LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_cdc.o(.data)
</UL>
<P><STRONG><a name="[4]"></a>USBD_CDC_EP0_RxReady</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, usbd_cdc.o(i.USBD_CDC_EP0_RxReady))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USBD_CDC_EP0_RxReady
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_cdc.o(.data)
</UL>
<P><STRONG><a name="[8]"></a>USBD_CDC_GetFSCfgDesc</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, usbd_cdc.o(i.USBD_CDC_GetFSCfgDesc))
<BR>[Address Reference Count : 1]<UL><LI> usbd_cdc.o(.data)
</UL>
<P><STRONG><a name="[7]"></a>USBD_CDC_GetHSCfgDesc</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, usbd_cdc.o(i.USBD_CDC_GetHSCfgDesc))
<BR>[Address Reference Count : 1]<UL><LI> usbd_cdc.o(.data)
</UL>
<P><STRONG><a name="[9]"></a>USBD_CDC_GetOtherSpeedCfgDesc</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, usbd_cdc.o(i.USBD_CDC_GetOtherSpeedCfgDesc))
<BR>[Address Reference Count : 1]<UL><LI> usbd_cdc.o(.data)
</UL>
<P><STRONG><a name="[1]"></a>USBD_CDC_Init</STRONG> (Thumb, 174 bytes, Stack size 24 bytes, usbd_cdc.o(i.USBD_CDC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = USBD_CDC_Init &rArr; USBD_LL_PrepareReceive &rArr; HAL_PCD_EP_Receive &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_PrepareReceive
<LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_OpenEP
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_cdc.o(.data)
</UL>
<P><STRONG><a name="[3]"></a>USBD_CDC_Setup</STRONG> (Thumb, 228 bytes, Stack size 32 bytes, usbd_cdc.o(i.USBD_CDC_Setup))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = USBD_CDC_Setup &rArr; USBD_CtlSendData &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendData
<LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlPrepareRx
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlError
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_cdc.o(.data)
</UL>
<P><STRONG><a name="[236]"></a>USBD_ClrFeature</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, usbd_ctlreq.o(i.USBD_ClrFeature))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = USBD_ClrFeature &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendStatus
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlError
</UL>
<BR>[Called By]<UL><LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdDevReq
</UL>

<P><STRONG><a name="[23d]"></a>USBD_GetConfig</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, usbd_ctlreq.o(i.USBD_GetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = USBD_GetConfig &rArr; USBD_CtlSendData &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendData
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlError
</UL>
<BR>[Called By]<UL><LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdDevReq
</UL>

<P><STRONG><a name="[23e]"></a>USBD_GetDescriptor</STRONG> (Thumb, 310 bytes, Stack size 24 bytes, usbd_ctlreq.o(i.USBD_GetDescriptor))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = USBD_GetDescriptor &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendStatus
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendData
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlError
</UL>
<BR>[Called By]<UL><LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdDevReq
</UL>

<P><STRONG><a name="[240]"></a>USBD_GetLen</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, usbd_ctlreq.o(i.USBD_GetLen))
<BR><BR>[Called By]<UL><LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_GetString
</UL>

<P><STRONG><a name="[23f]"></a>USBD_GetStatus</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, usbd_ctlreq.o(i.USBD_GetStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = USBD_GetStatus &rArr; USBD_CtlSendData &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendData
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlError
</UL>
<BR>[Called By]<UL><LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdDevReq
</UL>

<P><STRONG><a name="[24e]"></a>USBD_SetAddress</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, usbd_ctlreq.o(i.USBD_SetAddress))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = USBD_SetAddress &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_LL_SetUSBAddress
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendStatus
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlError
</UL>
<BR>[Called By]<UL><LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdDevReq
</UL>

<P><STRONG><a name="[24f]"></a>USBD_SetConfig</STRONG> (Thumb, 198 bytes, Stack size 16 bytes, usbd_ctlreq.o(i.USBD_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = USBD_SetConfig &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendStatus
<LI><a href="#[251]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_SetClassConfig
<LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_ClrClassConfig
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlError
</UL>
<BR>[Called By]<UL><LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdDevReq
</UL>

<P><STRONG><a name="[252]"></a>USBD_SetFeature</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, usbd_ctlreq.o(i.USBD_SetFeature))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = USBD_SetFeature &rArr; USBD_CtlSendStatus &rArr; USBD_LL_Transmit &rArr; HAL_PCD_EP_Transmit &rArr; USB_EPStartXfer &rArr; USB_WritePacket
</UL>
<BR>[Calls]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CtlSendStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_StdDevReq
</UL>

<P><STRONG><a name="[2cc]"></a>makeFreeRtosPriority</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, cmsis_os.o(i.makeFreeRtosPriority))
<BR><BR>[Called By]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osThreadCreate
</UL>

<P><STRONG><a name="[2f8]"></a>prvHeapInit</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, heap_4.o(i.prvHeapInit))
<BR><BR>[Called By]<UL><LI><a href="#[2f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
</UL>

<P><STRONG><a name="[2f9]"></a>prvInsertBlockIntoFreeList</STRONG> (Thumb, 78 bytes, Stack size 12 bytes, heap_4.o(i.prvInsertBlockIntoFreeList))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = prvInsertBlockIntoFreeList
</UL>
<BR>[Called By]<UL><LI><a href="#[2e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[2f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
</UL>

<P><STRONG><a name="[7c]"></a>prvTaskExitError</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, port.o(i.prvTaskExitError))
<BR>[Address Reference Count : 1]<UL><LI> port.o(i.pxPortInitialiseStack)
</UL>
<P><STRONG><a name="[2dd]"></a>prvCopyDataFromQueue</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, queue.o(i.prvCopyDataFromQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prvCopyDataFromQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[28b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[2ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
</UL>

<P><STRONG><a name="[2de]"></a>prvCopyDataToQueue</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, queue.o(i.prvCopyDataToQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = prvCopyDataToQueue &rArr; xTaskPriorityDisinherit
</UL>
<BR>[Calls]<UL><LI><a href="#[2df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityDisinherit
<LI><a href="#[28b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[321]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
<LI><a href="#[30c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[323]"></a>prvGetDisinheritPriorityAfterTimeout</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, queue.o(i.prvGetDisinheritPriorityAfterTimeout))
<BR><BR>[Called By]<UL><LI><a href="#[30d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
</UL>

<P><STRONG><a name="[2e1]"></a>prvInitialiseNewQueue</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, queue.o(i.prvInitialiseNewQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = prvInitialiseNewQueue &rArr; xQueueGenericReset &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[2e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
</UL>
<BR>[Called By]<UL><LI><a href="#[2d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreateStatic
<LI><a href="#[30b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
</UL>

<P><STRONG><a name="[2e7]"></a>prvIsQueueEmpty</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, queue.o(i.prvIsQueueEmpty))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prvIsQueueEmpty
</UL>
<BR>[Calls]<UL><LI><a href="#[2d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[30d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[2ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
</UL>

<P><STRONG><a name="[2e8]"></a>prvIsQueueFull</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, queue.o(i.prvIsQueueFull))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prvIsQueueFull
</UL>
<BR>[Calls]<UL><LI><a href="#[2d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[30c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[2f4]"></a>prvUnlockQueue</STRONG> (Thumb, 106 bytes, Stack size 24 bytes, queue.o(i.prvUnlockQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = prvUnlockQueue &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[2f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[2f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskMissedYield
<LI><a href="#[2d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[2f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
<LI><a href="#[30d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueSemaphoreTake
<LI><a href="#[2ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[30c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[2cf]"></a>prvAddCurrentTaskToDelayedList</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, tasks.o(i.prvAddCurrentTaskToDelayedList))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[2d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[2d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
<LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[313]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventListRestricted
<LI><a href="#[314]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[298]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulTaskNotifyTake
</UL>

<P><STRONG><a name="[2d3]"></a>prvAddNewTaskToReadyList</STRONG> (Thumb, 124 bytes, Stack size 16 bytes, tasks.o(i.prvAddNewTaskToReadyList))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = prvAddNewTaskToReadyList &rArr; prvInitialiseTaskLists
</UL>
<BR>[Calls]<UL><LI><a href="#[2d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[2d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[2d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseTaskLists
</UL>
<BR>[Called By]<UL><LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreateStatic
<LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>

<P><STRONG><a name="[2db]"></a>prvCheckTasksWaitingTermination</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, tasks.o(i.prvCheckTasksWaitingTermination))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = prvCheckTasksWaitingTermination &rArr; prvDeleteTCB &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[2d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[2dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvDeleteTCB
</UL>
<BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIdleTask
</UL>

<P><STRONG><a name="[2dc]"></a>prvDeleteTCB</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, tasks.o(i.prvDeleteTCB))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = prvDeleteTCB &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[2e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
</UL>
<BR>[Called By]<UL><LI><a href="#[2db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckTasksWaitingTermination
</UL>

<P><STRONG><a name="[7d]"></a>prvIdleTask</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, tasks.o(i.prvIdleTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = prvIdleTask &rArr; prvCheckTasksWaitingTermination &rArr; prvDeleteTCB &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[2db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckTasksWaitingTermination
</UL>
<BR>[Address Reference Count : 1]<UL><LI> tasks.o(i.vTaskStartScheduler)
</UL>
<P><STRONG><a name="[2e3]"></a>prvInitialiseNewTask</STRONG> (Thumb, 146 bytes, Stack size 32 bytes, tasks.o(i.prvInitialiseNewTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = prvInitialiseNewTask
</UL>
<BR>[Calls]<UL><LI><a href="#[2e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxPortInitialiseStack
<LI><a href="#[2e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialiseItem
</UL>
<BR>[Called By]<UL><LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreateStatic
<LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>

<P><STRONG><a name="[2d5]"></a>prvInitialiseTaskLists</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, tasks.o(i.prvInitialiseTaskLists))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = prvInitialiseTaskLists
</UL>
<BR>[Calls]<UL><LI><a href="#[2d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
</UL>
<BR>[Called By]<UL><LI><a href="#[2d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
</UL>

<P><STRONG><a name="[326]"></a>prvResetNextTaskUnblockTime</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, tasks.o(i.prvResetNextTaskUnblockTime))
<BR><BR>[Called By]<UL><LI><a href="#[31e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
<LI><a href="#[2f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
</UL>

<P><STRONG><a name="[325]"></a>prvSearchForNameWithinSingleList</STRONG> (Thumb, 90 bytes, Stack size 20 bytes, tasks.o(i.prvSearchForNameWithinSingleList))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = prvSearchForNameWithinSingleList
</UL>
<BR>[Called By]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetHandle
</UL>

<P><STRONG><a name="[2d7]"></a>prvCheckForValidListAndQueue</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, timers.o(i.prvCheckForValidListAndQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = prvCheckForValidListAndQueue &rArr; xQueueGenericCreateStatic &rArr; prvInitialiseNewQueue &rArr; xQueueGenericReset &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[2da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueAddToRegistry
<LI><a href="#[2d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
<LI><a href="#[2d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreateStatic
<LI><a href="#[2d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[317]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
</UL>

<P><STRONG><a name="[2f3]"></a>prvGetNextExpireTime</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, timers.o(i.prvGetNextExpireTime))
<BR><BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
</UL>

<P><STRONG><a name="[2e6]"></a>prvInsertTimerInActiveList</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, timers.o(i.prvInsertTimerInActiveList))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = prvInsertTimerInActiveList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[2d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
</UL>
<BR>[Called By]<UL><LI><a href="#[2eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[2e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessExpiredTimer
</UL>

<P><STRONG><a name="[2e9]"></a>prvProcessExpiredTimer</STRONG> (Thumb, 88 bytes, Stack size 24 bytes, timers.o(i.prvProcessExpiredTimer))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = prvProcessExpiredTimer &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[2ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
<LI><a href="#[2e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertTimerInActiveList
</UL>
<BR>[Called By]<UL><LI><a href="#[2ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
</UL>

<P><STRONG><a name="[2eb]"></a>prvProcessReceivedCommands</STRONG> (Thumb, 198 bytes, Stack size 40 bytes, timers.o(i.prvProcessReceivedCommands))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = prvProcessReceivedCommands &rArr; prvSampleTimeNow &rArr; prvSwitchTimerLists &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[2ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
<LI><a href="#[2ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[2e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[2ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
<LI><a href="#[2e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertTimerInActiveList
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
</UL>

<P><STRONG><a name="[2ee]"></a>prvProcessTimerOrBlockTask</STRONG> (Thumb, 100 bytes, Stack size 24 bytes, timers.o(i.prvProcessTimerOrBlockTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = prvProcessTimerOrBlockTask &rArr; prvSampleTimeNow &rArr; prvSwitchTimerLists &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[2f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
<LI><a href="#[2f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[2ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[2ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
<LI><a href="#[2e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessExpiredTimer
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
</UL>

<P><STRONG><a name="[2ec]"></a>prvSampleTimeNow</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, timers.o(i.prvSampleTimeNow))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = prvSampleTimeNow &rArr; prvSwitchTimerLists &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
<LI><a href="#[2f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSwitchTimerLists
</UL>
<BR>[Called By]<UL><LI><a href="#[2ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
<LI><a href="#[2eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
</UL>

<P><STRONG><a name="[2f2]"></a>prvSwitchTimerLists</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, timers.o(i.prvSwitchTimerLists))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = prvSwitchTimerLists &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[2d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
<LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[2ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[2ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
</UL>

<P><STRONG><a name="[7e]"></a>prvTimerTask</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, timers.o(i.prvTimerTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = prvTimerTask &rArr; prvProcessReceivedCommands &rArr; prvSampleTimeNow &rArr; prvSwitchTimerLists &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; xTaskRemoveFromEventList
</UL>
<BR>[Calls]<UL><LI><a href="#[2ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
<LI><a href="#[2eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[2f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvGetNextExpireTime
</UL>
<BR>[Address Reference Count : 1]<UL><LI> timers.o(i.xTimerCreateTimerTask)
</UL>
<P><STRONG><a name="[d]"></a>CDC_Control_FS</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, usbd_cdc_if.o(i.CDC_Control_FS))
<BR>[Address Reference Count : 1]<UL><LI> usbd_cdc_if.o(.data)
</UL>
<P><STRONG><a name="[c]"></a>CDC_DeInit_FS</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, usbd_cdc_if.o(i.CDC_DeInit_FS))
<BR>[Address Reference Count : 1]<UL><LI> usbd_cdc_if.o(.data)
</UL>
<P><STRONG><a name="[b]"></a>CDC_Init_FS</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, usbd_cdc_if.o(i.CDC_Init_FS))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CDC_Init_FS
</UL>
<BR>[Calls]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CDC_SetTxBuffer
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_CDC_SetRxBuffer
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_cdc_if.o(.data)
</UL>
<P><STRONG><a name="[11b]"></a>Get_SerialNum</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, usbd_desc.o(i.Get_SerialNum))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = Get_SerialNum &rArr; IntToUnicode
</UL>
<BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntToUnicode
</UL>
<BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBD_FS_SerialStrDescriptor
</UL>

<P><STRONG><a name="[11c]"></a>IntToUnicode</STRONG> (Thumb, 58 bytes, Stack size 20 bytes, usbd_desc.o(i.IntToUnicode))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = IntToUnicode
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_SerialNum
</UL>

<P><STRONG><a name="[1d4]"></a>imu_cali_slove</STRONG> (Thumb, 228 bytes, Stack size 36 bytes, INS_task.o(i.imu_cali_slove))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = imu_cali_slove
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_task
</UL>

<P><STRONG><a name="[105]"></a>imu_cmd_spi_dma</STRONG> (Thumb, 242 bytes, Stack size 8 bytes, INS_task.o(i.imu_cmd_spi_dma))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = imu_cmd_spi_dma &rArr; SPI1_DMA_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[2bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_DMA_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_Callback
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream2_IRQHandler
</UL>

<P><STRONG><a name="[1dc]"></a>imu_temp_control</STRONG> (Thumb, 152 bytes, Stack size 24 bytes, INS_task.o(i.imu_temp_control))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = imu_temp_control
</UL>
<BR>[Calls]<UL><LI><a href="#[2bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;imu_pwm_set
<LI><a href="#[2bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_control_temperature
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_calc
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_task
</UL>

<P><STRONG><a name="[306]"></a>ui_init</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, UI_task.o(i.ui_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = ui_init &rArr; osDelay &rArr; vTaskDelay &rArr; xTaskResumeAll &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;osDelay
<LI><a href="#[275]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Ungroup_5
<LI><a href="#[274]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Ungroup_4
<LI><a href="#[273]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Ungroup_3
<LI><a href="#[272]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Ungroup_2
<LI><a href="#[271]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Ungroup_1
<LI><a href="#[26e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Ungroup_0
<LI><a href="#[26d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Dynamic_3
<LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Dynamic_2
<LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Dynamic_1
<LI><a href="#[267]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ui_init_g_Dynamic_0
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ui_task
</UL>

<P><STRONG><a name="[208]"></a>RC_cmd_to_calibrate</STRONG> (Thumb, 566 bytes, Stack size 24 bytes, calibrate_task.o(i.RC_cmd_to_calibrate))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = RC_cmd_to_calibrate &rArr; get_temprate &rArr; adcx_get_chx_value &rArr; HAL_ADC_PollForConversion
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN_cmd_chassis_reset_ID
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_temprate
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buzzer_on
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buzzer_off
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calibrate_task
</UL>

<P><STRONG><a name="[288]"></a>cali_data_read</STRONG> (Thumb, 130 bytes, Stack size 32 bytes, calibrate_task.o(i.cali_data_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = cali_data_read
</UL>
<BR>[Calls]<UL><LI><a href="#[289]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_read
</UL>
<BR>[Called By]<UL><LI><a href="#[293]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cali_param_init
</UL>

<P><STRONG><a name="[28a]"></a>cali_data_write</STRONG> (Thumb, 100 bytes, Stack size 24 bytes, calibrate_task.o(i.cali_data_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = cali_data_write &rArr; flash_erase_address &rArr; HAL_FLASHEx_Erase &rArr; FLASH_WaitForLastOperation
</UL>
<BR>[Calls]<UL><LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_write_single_address
<LI><a href="#[28c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_erase_address
<LI><a href="#[28b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calibrate_task
</UL>

<P><STRONG><a name="[1b]"></a>cali_gimbal_hook</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, calibrate_task.o(i.cali_gimbal_hook))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = cali_gimbal_hook &rArr; set_cali_gimbal_hook &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_cali_gimbal_hook
<LI><a href="#[28f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_cali_gimbal_hook
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buzzer_on
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buzzer_off
</UL>
<BR>[Address Reference Count : 1]<UL><LI> calibrate_task.o(.data)
</UL>
<P><STRONG><a name="[1c]"></a>cali_gyro_hook</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, calibrate_task.o(i.cali_gyro_hook))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = cali_gyro_hook &rArr; INS_cali_gyro
</UL>
<BR>[Calls]<UL><LI><a href="#[290]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_set_cali_gyro
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_cali_gyro
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buzzer_on
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buzzer_off
<LI><a href="#[292]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RC_unable
<LI><a href="#[291]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RC_restart
</UL>
<BR>[Address Reference Count : 1]<UL><LI> calibrate_task.o(.data)
</UL>
<P><STRONG><a name="[1a]"></a>cali_head_hook</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, calibrate_task.o(i.cali_head_hook))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = cali_head_hook &rArr; get_temprate &rArr; adcx_get_chx_value &rArr; HAL_ADC_PollForConversion
</UL>
<BR>[Calls]<UL><LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_temprate
</UL>
<BR>[Address Reference Count : 1]<UL><LI> calibrate_task.o(.data)
</UL>
<P><STRONG><a name="[29a]"></a>girt_powerLimit</STRONG> (Thumb, 1534 bytes, Stack size 104 bytes, chassis_task.o(i.girt_powerLimit))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = girt_powerLimit &rArr; square &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[2b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_chassis_power_limit
<LI><a href="#[2b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_chassis_power_buffer
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[2ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;square
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;chassis_task
</UL>

<P><STRONG><a name="[29b]"></a>detect_init</STRONG> (Thumb, 122 bytes, Stack size 120 bytes, detect_task.o(i.detect_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = detect_init
</UL>
<BR>[Calls]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;detect_task
</UL>

<P><STRONG><a name="[2a3]"></a>gimbal_absolute_angle_control</STRONG> (Thumb, 168 bytes, Stack size 20 bytes, gimbal_behaviour.o(i.gimbal_absolute_angle_control))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gimbal_absolute_angle_control
</UL>
<BR>[Called By]<UL><LI><a href="#[2a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_behaviour_control_set
</UL>

<P><STRONG><a name="[2aa]"></a>gimbal_behavour_set</STRONG> (Thumb, 244 bytes, Stack size 32 bytes, gimbal_behaviour.o(i.gimbal_behavour_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = gimbal_behavour_set &rArr; __hardfp_fabs
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;toe_is_error
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[2a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_behaviour_mode_set
</UL>

<P><STRONG><a name="[2a6]"></a>gimbal_cali_control</STRONG> (Thumb, 296 bytes, Stack size 20 bytes, gimbal_behaviour.o(i.gimbal_cali_control))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gimbal_cali_control
</UL>
<BR>[Called By]<UL><LI><a href="#[2a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_behaviour_control_set
</UL>

<P><STRONG><a name="[2a5]"></a>gimbal_init_control</STRONG> (Thumb, 122 bytes, Stack size 24 bytes, gimbal_behaviour.o(i.gimbal_init_control))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = gimbal_init_control &rArr; __hardfp_fabs
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[2a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_behaviour_control_set
</UL>

<P><STRONG><a name="[2a8]"></a>gimbal_motionless_control</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gimbal_behaviour.o(i.gimbal_motionless_control))
<BR><BR>[Called By]<UL><LI><a href="#[2a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_behaviour_control_set
</UL>

<P><STRONG><a name="[2a7]"></a>gimbal_relative_angle_control</STRONG> (Thumb, 144 bytes, Stack size 20 bytes, gimbal_behaviour.o(i.gimbal_relative_angle_control))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gimbal_relative_angle_control
</UL>
<BR>[Called By]<UL><LI><a href="#[2a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_behaviour_control_set
</UL>

<P><STRONG><a name="[2a4]"></a>gimbal_zero_force_control</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gimbal_behaviour.o(i.gimbal_zero_force_control))
<BR><BR>[Called By]<UL><LI><a href="#[2a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_behaviour_control_set
</UL>

<P><STRONG><a name="[286]"></a>calc_gimbal_cali</STRONG> (Thumb, 326 bytes, Stack size 40 bytes, gimbal_task.o(i.calc_gimbal_cali))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = calc_gimbal_cali
</UL>
<BR>[Calls]<UL><LI><a href="#[287]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_ecd_to_angle_change
</UL>
<BR>[Called By]<UL><LI><a href="#[28f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmd_cali_gimbal_hook
</UL>

<P><STRONG><a name="[2a1]"></a>gimbal_absolute_angle_limit</STRONG> (Thumb, 204 bytes, Stack size 24 bytes, gimbal_task.o(i.gimbal_absolute_angle_limit))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = gimbal_absolute_angle_limit
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;loop_fp32_constrain
</UL>
<BR>[Called By]<UL><LI><a href="#[2b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_set_control
</UL>

<P><STRONG><a name="[2ab]"></a>gimbal_control_loop</STRONG> (Thumb, 564 bytes, Stack size 16 bytes, gimbal_task.o(i.gimbal_control_loop))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = gimbal_control_loop &rArr; New_PID_cail &rArr; activation_function &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;New_PID_cail
<LI><a href="#[2ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_motor_raw_angle_control
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_task
</UL>

<P><STRONG><a name="[2ad]"></a>gimbal_feedback_update</STRONG> (Thumb, 256 bytes, Stack size 32 bytes, gimbal_task.o(i.gimbal_feedback_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = gimbal_feedback_update &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_sin_f32
<LI><a href="#[287]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_ecd_to_angle_change
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cos_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_task
<LI><a href="#[2ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_init
</UL>

<P><STRONG><a name="[2ae]"></a>gimbal_init</STRONG> (Thumb, 1002 bytes, Stack size 48 bytes, gimbal_task.o(i.gimbal_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = gimbal_init &rArr; gimbal_feedback_update &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[2af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_yaw_gimbal_motor_measure_point
<LI><a href="#[2b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_pitch_gimbal_motor_measure_point
<LI><a href="#[2b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_gyro_data_point
<LI><a href="#[2b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_INS_angle_point
<LI><a href="#[294]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_remote_control_point
<LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;New_PID_init
<LI><a href="#[2ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_feedback_update
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_task
</UL>

<P><STRONG><a name="[2b7]"></a>gimbal_mode_change_control_transit</STRONG> (Thumb, 200 bytes, Stack size 0 bytes, gimbal_task.o(i.gimbal_mode_change_control_transit))
<BR><BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_task
</UL>

<P><STRONG><a name="[2ac]"></a>gimbal_motor_raw_angle_control</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gimbal_task.o(i.gimbal_motor_raw_angle_control))
<BR><BR>[Called By]<UL><LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_control_loop
</UL>

<P><STRONG><a name="[2b4]"></a>gimbal_relative_angle_limit</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, gimbal_task.o(i.gimbal_relative_angle_limit))
<BR><BR>[Called By]<UL><LI><a href="#[2b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_set_control
</UL>

<P><STRONG><a name="[2b3]"></a>gimbal_set_control</STRONG> (Thumb, 198 bytes, Stack size 32 bytes, gimbal_task.o(i.gimbal_set_control))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = gimbal_set_control &rArr; GIMBAL_AUTO_Mode_Ctrl &rArr; Vision_Error_Angle_YAW &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GIMBAL_AUTO_Mode_Ctrl
<LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_relative_angle_limit
<LI><a href="#[2a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_absolute_angle_limit
<LI><a href="#[2a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_behaviour_control_set
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_task
</UL>

<P><STRONG><a name="[2b5]"></a>gimbal_set_mode</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gimbal_task.o(i.gimbal_set_mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = gimbal_set_mode &rArr; gimbal_behaviour_mode_set &rArr; gimbal_behavour_set &rArr; __hardfp_fabs
</UL>
<BR>[Calls]<UL><LI><a href="#[2a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_behaviour_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_task
</UL>

<P><STRONG><a name="[287]"></a>motor_ecd_to_angle_change</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, gimbal_task.o(i.motor_ecd_to_angle_change))
<BR><BR>[Called By]<UL><LI><a href="#[2ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gimbal_feedback_update
<LI><a href="#[286]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_gimbal_cali
</UL>

<P><STRONG><a name="[1df]"></a>LED_oncar</STRONG> (Thumb, 328 bytes, Stack size 48 bytes, led_flow_task.o(i.LED_oncar))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = LED_oncar &rArr; CAN_cmd_RGB &rArr; HAL_CAN_AddTxMessage
</UL>
<BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pm01_get_vout
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN_cmd_RGB
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fric_open_off
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vision_Get_Fucking_FLAG
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;REVOLVER_open_off
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;toe_is_error
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_RGB_flow_task
</UL>

<P><STRONG><a name="[2fc]"></a>referee_unpack_fifo_data</STRONG> (Thumb, 290 bytes, Stack size 16 bytes, referee_usart_task.o(i.referee_unpack_fifo_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = referee_unpack_fifo_data &rArr; verify_CRC8_check_sum &rArr; get_CRC8_check_sum
</UL>
<BR>[Calls]<UL><LI><a href="#[2fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;verify_CRC8_check_sum
<LI><a href="#[2ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;verify_CRC16_check_sum
<LI><a href="#[300]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fifo_s_used
<LI><a href="#[2fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fifo_s_get
<LI><a href="#[2fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;referee_data_solve
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;referee_usart_task
</UL>

<P><STRONG><a name="[22a]"></a>sbus_to_rc</STRONG> (Thumb, 180 bytes, Stack size 20 bytes, remote_control.o(i.sbus_to_rc))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = sbus_to_rc
</UL>
<BR>[Called By]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_IRQHandler
</UL>

<P><STRONG><a name="[285]"></a>buzzer_warn_error</STRONG> (Thumb, 128 bytes, Stack size 16 bytes, test_task.o(i.buzzer_warn_error))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = buzzer_warn_error
</UL>
<BR>[Calls]<UL><LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buzzer_on
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;buzzer_off
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test_task
</UL>

<P><STRONG><a name="[30e]"></a>__ARM_isnanf</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, usb_task.o(i.__ARM_isnanf))
<BR><BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_read_data
</UL>

<P><STRONG><a name="[281]"></a>adcx_get_chx_value</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, bsp_adc.o(i.adcx_get_chx_value))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = adcx_get_chx_value &rArr; HAL_ADC_PollForConversion
</UL>
<BR>[Calls]<UL><LI><a href="#[282]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Start
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_PollForConversion
<LI><a href="#[283]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_GetValue
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_temprate
</UL>

<P><STRONG><a name="[29c]"></a>ger_sector</STRONG> (Thumb, 158 bytes, Stack size 0 bytes, bsp_flash.o(i.ger_sector))
<BR><BR>[Called By]<UL><LI><a href="#[28c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;flash_erase_address
</UL>

<P><STRONG><a name="[205]"></a>activation_function</STRONG> (Thumb, 162 bytes, Stack size 24 bytes, New_pid.o(i.activation_function))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = activation_function &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[280]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_abs
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;New_PID_cail
</UL>

<P><STRONG><a name="[204]"></a>deal_err</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, New_pid.o(i.deal_err))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = deal_err
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;loop_fp32_constrain
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;New_PID_cail
</UL>

<P><STRONG><a name="[280]"></a>pid_abs</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, New_pid.o(i.pid_abs))
<BR><BR>[Called By]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;activation_function
</UL>

<P><STRONG><a name="[206]"></a>pid_max_limit</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, New_pid.o(i.pid_max_limit))
<BR><BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;New_PID_cail
</UL>

<P><STRONG><a name="[cd]"></a>BMI088_read_muli_reg</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, BMI088driver.o(i.BMI088_read_muli_reg))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = BMI088_read_muli_reg &rArr; BMI088_read_write_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_read_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_read
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_accel_self_test
</UL>

<P><STRONG><a name="[ce]"></a>BMI088_read_single_reg</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, BMI088driver.o(i.BMI088_read_single_reg))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = BMI088_read_single_reg &rArr; BMI088_read_write_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_read_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_gyro_self_test
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_gyro_init
</UL>

<P><STRONG><a name="[d0]"></a>BMI088_write_single_reg</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, BMI088driver.o(i.BMI088_write_single_reg))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = BMI088_write_single_reg &rArr; BMI088_read_write_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BMI088_read_write_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_gyro_self_test
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_gyro_init
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_accel_self_test
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmi088_accel_init
</UL>

<P><STRONG><a name="[27f]"></a>AHRS_fabs</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, ahrs.o(i.AHRS_fabs))
<BR><BR>[Called By]<UL><LI><a href="#[27e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_update_kp_ki
</UL>

<P><STRONG><a name="[b3]"></a>accel_comple_filter</STRONG> (Thumb, 454 bytes, Stack size 64 bytes, ahrs.o(i.accel_comple_filter))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = accel_comple_filter &rArr; accel_update_kp_ki &rArr; AHRS_invSqrt &rArr; __hardfp_sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_invSqrt
<LI><a href="#[27e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_update_kp_ki
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_update
</UL>

<P><STRONG><a name="[27e]"></a>accel_update_kp_ki</STRONG> (Thumb, 370 bytes, Stack size 40 bytes, ahrs.o(i.accel_update_kp_ki))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = accel_update_kp_ki &rArr; AHRS_invSqrt &rArr; __hardfp_sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_invSqrt
<LI><a href="#[27f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_comple_filter
</UL>

<P><STRONG><a name="[af]"></a>angle_to_quat</STRONG> (Thumb, 364 bytes, Stack size 48 bytes, ahrs.o(i.angle_to_quat))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = angle_to_quat
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_sinf
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_cosf
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_init
</UL>

<P><STRONG><a name="[b6]"></a>quat_normalization</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, ahrs.o(i.quat_normalization))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = quat_normalization &rArr; AHRS_invSqrt &rArr; __hardfp_sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_invSqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_update
</UL>

<P><STRONG><a name="[b5]"></a>update_w</STRONG> (Thumb, 122 bytes, Stack size 0 bytes, ahrs.o(i.update_w))
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AHRS_update
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
