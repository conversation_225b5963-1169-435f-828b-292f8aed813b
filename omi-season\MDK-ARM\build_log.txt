*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'C:\Keil_v5\ARM\ARMCC\Bin'
Build target 'VESTA'
compiling bsp_i2c.c...
compiling detect_task.c...
compiling main.c...
compiling freertos.c...
compiling bsp_delay.c...
compiling bsp_fric.c...
compiling bsp_imu_pwm.c...
compiling bsp_laser.c...
compiling bsp_can.c...
compiling bsp_flash.c...
compiling bsp_spi.c...
compiling bsp_rng.c...
compiling bsp_led.c...
compiling bsp_buzzer.c...
compiling bsp_adc.c...
compiling referee.c...
compiling remote_control.c...
compiling bsp_usart.c...
compiling bsp_rc.c...
compiling bsp_servo_pwm.c...
compiling bsp_crc32.c...
compiling chassis_behaviour.c...
compiling gimbal_behaviour.c...
compiling test_task.c...
compiling stm32f4xx_it.c...
compiling INS_task.c...
compiling gimbal_task.c...
compiling referee_usart_task.c...
compiling CAN_receive.c...
compiling usb_task.c...
compiling calibrate_task.c...
compiling led_flow_task.c...
compiling chassis_task.c...
compiling UI_task.c...
compiling vision.c...
compiling ist8310driver.c...
compiling user_lib.c...
compiling kalman_filter.c...
compiling New_pid.c...
compiling RM_Cilent_UI.c...
compiling music_task.c...
compiling OLED.c...
compiling BMI088driver.c...
compiling shoot_task.c...
compiling voltage_task.c...
compiling BMI088Middleware.c...
compiling AHRS_middleware.c...
compiling ist8310driver_middleware.c...
compiling Vision_task.c...
compiling CRC8_CRC16.c...
compiling fifo.c...
compiling mem_mang4.c...
compiling filter.c...
compiling mymath.c...
compiling ramp.c...
compiling pid.c...
compiling stm32f4xx_hal_pcd_ex.c...
compiling stm32f4xx_hal_pcd.c...
compiling stm32f4xx_hal_rcc.c...
compiling stm32f4xx_ll_usb.c...
compiling stm32f4xx_hal_rcc_ex.c...
compiling stm32f4xx_hal_flash_ex.c...
compiling stm32f4xx_hal_flash.c...
compiling stm32f4xx_hal_flash_ramfunc.c...
compiling stm32f4xx_hal_gpio.c...
compiling stm32f4xx_hal_dma.c...
compiling stm32f4xx_hal_dma_ex.c...
compiling stm32f4xx_hal_pwr.c...
compiling stm32f4xx_hal_pwr_ex.c...
compiling croutine.c...
compiling event_groups.c...
compiling list.c...
compiling stm32f4xx_hal_cortex.c...
compiling stm32f4xx_hal_exti.c...
compiling queue.c...
compiling stm32f4xx_hal.c...
compiling stream_buffer.c...
compiling stm32f4xx_hal_adc_ex.c...
compiling tasks.c...
compiling stm32f4xx_hal_adc.c...
compiling timers.c...
compiling stm32f4xx_hal_crc.c...
compiling stm32f4xx_hal_can.c...
compiling heap_4.c...
compiling cmsis_os.c...
compiling stm32f4xx_hal_i2c_ex.c...
compiling stm32f4xx_hal_rng.c...
compiling port.c...
compiling stm32f4xx_hal_rtc.c...
compiling stm32f4xx_hal_i2c.c...
compiling stm32f4xx_hal_rtc_ex.c...
compiling stm32f4xx_hal_spi.c...
compiling stm32f4xx_hal_tim_ex.c...
compiling stm32f4xx_hal_tim.c...
compiling stm32f4xx_hal_uart.c...
compiling usbd_core.c...
compiling usbd_ctlreq.c...
compiling usbd_ioreq.c...
compiling usbd_cdc.c...
compiling ui_interface.c...
compiling ui_g_Ungroup_0.c...
compiling ui_g_Dynamic_0.c...
compiling ui_g_Dynamic_1.c...
compiling ui_g_Ungroup_1.c...
compiling ui_g_Dynamic_2.c...
compiling ui_g_Ungroup_2.c...
compiling ui_g_Dynamic_3.c...
compiling ui_g_Ungroup_3.c...
compiling ui_g_Ungroup_4.c...
compiling ui_g_Ungroup_5.c...
linking...
Program Size: Code=93356 RO-data=5044 RW-data=3180 ZI-data=63292  
FromELF: creating hex file...
".\VESTA\VESTA.axf" - 0 Error(s), 0 Warning(s).
Build Time Elapsed:  00:00:11
