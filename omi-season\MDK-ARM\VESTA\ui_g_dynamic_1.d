.\vesta\ui_g_dynamic_1.o: ..\application\UI\codes\ui_g_Dynamic_1.c
.\vesta\ui_g_dynamic_1.o: ..\application\UI\codes\ui_g_Dynamic_1.h
.\vesta\ui_g_dynamic_1.o: ..\application\UI\ui_interface.h
.\vesta\ui_g_dynamic_1.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\vesta\ui_g_dynamic_1.o: ..\application\UI\ui_types.h
.\vesta\ui_g_dynamic_1.o: ..\application\struct_typedef.h
.\vesta\ui_g_dynamic_1.o: ../Inc/usart.h
.\vesta\ui_g_dynamic_1.o: ../Inc/main.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal.h
.\vesta\ui_g_dynamic_1.o: ../Inc/stm32f4xx_hal_conf.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_rcc.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_def.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/CMSIS/Device/ST/STM32F4xx/Include/stm32f4xx.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/CMSIS/Device/ST/STM32F4xx/Include/stm32f407xx.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/CMSIS/Include/core_cm4.h
.\vesta\ui_g_dynamic_1.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/CMSIS/Include/cmsis_version.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/CMSIS/Include/cmsis_compiler.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/CMSIS/Include/cmsis_armcc.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/CMSIS/Include/mpu_armv7.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/CMSIS/Device/ST/STM32F4xx/Include/system_stm32f4xx.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h
.\vesta\ui_g_dynamic_1.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stddef.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_rcc_ex.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_exti.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_gpio.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_gpio_ex.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_dma.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_dma_ex.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_cortex.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_adc.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_adc_ex.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_can.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_crc.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_flash.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_flash_ex.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_flash_ramfunc.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_i2c.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_i2c_ex.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_pwr.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_pwr_ex.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_rng.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_rtc.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_rtc_ex.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_spi.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_tim.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_tim_ex.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_uart.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_pcd.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_ll_usb.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/STM32F4xx_HAL_Driver/Inc/stm32f4xx_hal_pcd_ex.h
.\vesta\ui_g_dynamic_1.o: ..\bsp\boards\bsp_usart.h
.\vesta\ui_g_dynamic_1.o: ..\application\shoot_task.h
.\vesta\ui_g_dynamic_1.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
.\vesta\ui_g_dynamic_1.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
.\vesta\ui_g_dynamic_1.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdbool.h
.\vesta\ui_g_dynamic_1.o: ..\application\usb_task.h
.\vesta\ui_g_dynamic_1.o: ..\application\remote_control.h
.\vesta\ui_g_dynamic_1.o: ..\bsp\boards\bsp_rc.h
.\vesta\ui_g_dynamic_1.o: ..\application\chassis_task.h
.\vesta\ui_g_dynamic_1.o: ..\application\CAN_receive.h
.\vesta\ui_g_dynamic_1.o: ..\application\gimbal_task.h
.\vesta\ui_g_dynamic_1.o: ..\components\controller\pid.h
.\vesta\ui_g_dynamic_1.o: ..\application\kalman_filter.h
.\vesta\ui_g_dynamic_1.o: ..\components\algorithm\Include\arm_math.h
.\vesta\ui_g_dynamic_1.o: ../Drivers/CMSIS/Include/core_cm4.h
.\vesta\ui_g_dynamic_1.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\vesta\ui_g_dynamic_1.o: ..\application\kalman.h
.\vesta\ui_g_dynamic_1.o: ..\components\support\filter.h
.\vesta\ui_g_dynamic_1.o: ..\components\algorithm\user_lib.h
.\vesta\ui_g_dynamic_1.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdarg.h
.\vesta\ui_g_dynamic_1.o: ..\application\referee.h
.\vesta\ui_g_dynamic_1.o: ..\application\UI\RM_Cilent_UI.h
.\vesta\ui_g_dynamic_1.o: ..\application\protocol\protocol.h
